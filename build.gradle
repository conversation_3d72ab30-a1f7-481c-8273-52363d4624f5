
buildscript {
    repositories {
        gradlePluginPortal();
    }
    dependencies {
        //sdk引入6.10.0Plugin,如使用最新版本，请参考官网文档最新版本号
        classpath 'com.bytedance.applog:RangersAppLog-All-plugin:6.10.0'
    }
}

plugins {
    id 'com.android.application' version '7.1.2' apply false
    id 'com.android.library' version '7.1.2' apply false
}


task clean(type: Delete) {
    delete rootProject.buildDir
}


