
说明：

1、集成sdk参照火山引擎官网链接：
https://www.volcengine.com/docs/6285/65980

2、集成基本流程：

引用maven仓库+plugin ——> sdk初始化 ——> 上报埋点事件

3、该demo支持SAAS环境完成以下场景的接入：

- 上报用户事件 //模拟匿名用户、实体用户
- 上报用户属性
- 上报事件公共属性
- 绑定业务对象事件上报
- 广告检测集成
- 如何获取火山生成的设备id&SSID

ps: AS版本不同引入maven仓库、添加classPath方式有所不同 //该demo是以新版本方式进行引入的

- 新版本maven仓库引用添加到settings.gradle
- 新版本添加classPath到项目级别gradle中
- 老版本参照官网链接

4、该demo需要修改的关键信息，修改Constant类配置信息
    //Appid,请修改对接的具体应用
    appId = "";
