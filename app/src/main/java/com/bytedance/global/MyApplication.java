package com.bytedance.global;

import android.app.Application;
import android.util.Log;

import com.bytedance.applog.AppLogUtils;
import com.bytedance.applog.AppLogTest;
import com.bytedance.util.Constant;

/**
 * 全局系统变量
 * 集成AppLog封装库的初始化
 */
public class MyApplication extends Application {

    private static final String TAG = "MyApplication";
    private String userId;

    @Override
    public void onCreate() {
        super.onCreate();

        // 初始化AppLog封装库
        initAppLog();

        // 运行测试（可选，生产环境应该移除）
        if (isDebugMode()) {
            runAppLogTests();
        }
    }

    /**
     * 初始化AppLog
     */
    private void initAppLog() {
        try {
            if (isDebugMode()) {
                // 开发环境：启用日志，关闭加密
                AppLogUtils.initDev(this, Constant.appId);
                Log.i(TAG, "AppLog开发环境初始化完成");
            } else {
                // 生产环境：关闭日志，启用加密
                AppLogUtils.initProd(this, Constant.appId);
                Log.i(TAG, "AppLog生产环境初始化完成");
            }
        } catch (Exception e) {
            Log.e(TAG, "AppLog初始化失败", e);
        }
    }

    /**
     * 运行AppLog测试
     */
    private void runAppLogTests() {
        // 延迟执行测试，确保初始化完成
        new Thread(() -> {
            try {
                Thread.sleep(2000); // 等待2秒
                AppLogTest.runAllTests(this);
            } catch (InterruptedException e) {
                Log.e(TAG, "测试线程被中断", e);
            }
        }).start();
    }

    /**
     * 判断是否为调试模式
     */
    private boolean isDebugMode() {
        // 这里可以根据BuildConfig.DEBUG或其他方式判断
        return true; // 示例中设为true，实际项目中应该使用BuildConfig.DEBUG
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
        // 设置用户ID时同时更新AppLog
        if (userId != null) {
            AppLogUtils.setUserId(userId);
            Log.i(TAG, "用户ID已设置: " + userId);
        }
    }
}
