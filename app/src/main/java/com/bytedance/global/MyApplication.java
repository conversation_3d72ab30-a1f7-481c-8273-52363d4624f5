package com.bytedance.global;

import android.app.Application;
import android.util.Log;

import com.bytedance.applog.AppLogUtils;
import com.bytedance.applog.AppLogTest;
import com.bytedance.util.Constant;
import kotlinx.coroutines.CoroutineScope;
import kotlinx.coroutines.Dispatchers;
import kotlinx.coroutines.SupervisorJob;
import kotlinx.coroutines.launch;

/**
 * 全局系统变量
 * 集成AppLog封装库的初始化
 */
public class MyApplication extends Application {

    private static final String TAG = "MyApplication";
    private String userId;

    // 协程作用域
    private final CoroutineScope scope = CoroutineScope.create(Dispatchers.getMain().plus(new SupervisorJob()));

    @Override
    public void onCreate() {
        super.onCreate();

        // 初始化AppLog封装库
        initAppLog();

        // 运行测试（可选，生产环境应该移除）
        if (isDebugMode()) {
            runAppLogTests();
        }
    }

    /**
     * 初始化AppLog - 使用Kotlin版本
     */
    private void initAppLog() {
        try {
            if (isDebugMode()) {
                // 使用Kotlin版本初始化（开发环境）
                AppLogUtils.INSTANCE.initDev(this, Constant.appId);
                Log.i(TAG, "AppLog Kotlin版本开发环境初始化完成");
            } else {
                // 使用Kotlin版本初始化（生产环境）
                AppLogUtils.INSTANCE.initProd(this, Constant.appId);
                Log.i(TAG, "AppLog Kotlin版本生产环境初始化完成");
            }
        } catch (Exception e) {
            Log.e(TAG, "AppLog初始化失败", e);
        }
    }

    /**
     * 运行AppLog测试 - 使用Kotlin版本
     */
    private void runAppLogTests() {
        // 使用Kotlin版本测试（使用协程）
        scope.launch(Dispatchers.getIO(), null, (scope, continuation) -> {
            try {
                Thread.sleep(2000); // 等待2秒
                AppLogTest.INSTANCE.runAllTests(MyApplication.this, continuation);
                Log.i(TAG, "Kotlin版本测试完成");
                return null;
            } catch (Exception e) {
                Log.e(TAG, "Kotlin测试失败", e);
                return null;
            }
        });
    }

    /**
     * 判断是否为调试模式
     */
    private boolean isDebugMode() {
        // 这里可以根据BuildConfig.DEBUG或其他方式判断
        return true; // 示例中设为true，实际项目中应该使用BuildConfig.DEBUG
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
        // 设置用户ID时同时更新AppLog
        if (userId != null) {
            AppLogUtils.setUserId(userId);
            Log.i(TAG, "用户ID已设置: " + userId);
        }
    }
}
