package com.bytedance.applog;

import android.app.Application;
import android.util.Log;

import androidx.annotation.Nullable;

import com.bytedance.applog.AppLog;
import com.bytedance.applog.ILogger;
import com.bytedance.applog.InitConfig;
import com.bytedance.applog.alink.IALinkListener;
import com.bytedance.applog.picker.Picker;

import java.util.Map;

/**
 * AppLog统一管理器
 * 提供AppLog SDK的统一初始化和管理功能
 * 
 * <AUTHOR>
 */
public class AppLogManager {
    
    private static final String TAG = "AppLogManager";
    private static AppLogManager instance;
    private boolean isInitialized = false;
    private AppLogConfig config;
    private Application application;
    
    // 监听器接口
    public interface InitializationListener {
        void onInitSuccess();
        void onInitFailure(Exception e);
    }
    
    public interface ALinkListener {
        void onALinkData(@Nullable Map<String, String> data, @Nullable Exception error);
        void onAttributionData(@Nullable Map<String, String> data, @Nullable Exception error);
    }
    
    private AppLogManager() {}
    
    public static synchronized AppLogManager getInstance() {
        if (instance == null) {
            instance = new AppLogManager();
        }
        return instance;
    }
    
    /**
     * 初始化AppLog SDK
     */
    public void initialize(Application application, AppLogConfig config) {
        initialize(application, config, null);
    }
    
    /**
     * 初始化AppLog SDK（带监听器）
     */
    public void initialize(Application application, AppLogConfig config, InitializationListener listener) {
        if (isInitialized) {
            Log.w(TAG, "AppLog is already initialized");
            if (listener != null) {
                listener.onInitSuccess();
            }
            return;
        }
        
        try {
            this.application = application;
            this.config = config;
            
            // 创建初始化配置
            InitConfig initConfig = createInitConfig(application, config);
            
            // 设置加密和压缩
            AppLog.setEncryptAndCompress(config.isEncryptEnabled());
            
            // 设置剪切板功能
            if (config.isClipboardEnabled()) {
                AppLog.setClipboardEnabled(true);
            }
            
            // 初始化SDK
            AppLog.init(application, initConfig);
            
            isInitialized = true;
            Log.i(TAG, "AppLog initialized successfully");
            
            if (listener != null) {
                listener.onInitSuccess();
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize AppLog", e);
            if (listener != null) {
                listener.onInitFailure(e);
            }
        }
    }
    
    /**
     * 创建初始化配置
     */
    private InitConfig createInitConfig(Application application, AppLogConfig config) {
        InitConfig initConfig = new InitConfig(config.getAppId(), config.getChannel());
        
        // 设置URI配置
        initConfig.setUriConfig(config.createUriConfig());
        
        // 设置AB测试
        initConfig.setAbEnable(config.isAbEnabled());
        
        // 设置日志
        initConfig.setLogEnable(config.isLogEnabled());
        if (config.isLogEnabled()) {
            initConfig.setLogger(new ILogger() {
                @Override
                public void log(String s, Throwable throwable) {
                    Log.d("AppLog", s);
                }
            });
        }
        
        // 设置圈选埋点
        if (config.isPickerEnabled()) {
            initConfig.setPicker(new Picker(application, initConfig));
        }
        
        // 设置自动埋点
        initConfig.setAutoTrackEnabled(config.isAutoTrackEnabled());
        initConfig.setAutoTrackFragmentEnabled(config.isAutoTrackFragmentEnabled());
        
        // 设置H5相关配置
        initConfig.setH5BridgeEnable(config.isH5BridgeEnabled());
        initConfig.setH5CollectEnable(config.isH5CollectEnabled());
        if (config.getH5BridgeAllowlist() != null) {
            initConfig.setH5BridgeAllowlist(config.getH5BridgeAllowlist());
        }
        
        // 设置自动启动
        initConfig.setAutoStart(config.isAutoStart());
        
        // 设置用户ID
        if (config.getUserUniqueId() != null) {
            initConfig.setUserUniqueId(config.getUserUniqueId());
        }
        
        // 启用延迟深度链接
        if (config.isDeferredALinkEnabled()) {
            initConfig.enableDeferredALink();
        }
        
        return initConfig;
    }
    
    /**
     * 设置ALink监听器
     */
    public void setALinkListener(ALinkListener listener) {
        if (listener != null) {
            AppLog.setALinkListener(new IALinkListener() {
                @Override
                public void onALinkData(@Nullable Map<String, String> map, @Nullable Exception e) {
                    listener.onALinkData(map, e);
                }
                
                @Override
                public void onAttributionData(@Nullable Map<String, String> map, @Nullable Exception e) {
                    listener.onAttributionData(map, e);
                }
            });
        }
    }
    
    /**
     * 获取设备ID
     */
    public String getDeviceId() {
        if (!isInitialized) {
            Log.w(TAG, "AppLog not initialized, device ID may not be available");
        }
        return AppLog.getDid();
    }
    
    /**
     * 获取SSID
     */
    public String getSSID() {
        if (!isInitialized) {
            Log.w(TAG, "AppLog not initialized, SSID may not be available");
        }
        return AppLog.getSsid();
    }
    
    /**
     * 等待设备注册完成并获取设备ID
     */
    public void getDeviceIdAsync(DeviceIdCallback callback) {
        new Thread(() -> {
            try {
                // 等待设备注册完成
                Thread.sleep(1200);
                String deviceId = getDeviceId();
                String ssid = getSSID();
                
                if (callback != null) {
                    callback.onDeviceIdReady(deviceId, ssid);
                }
                
            } catch (InterruptedException e) {
                Log.e(TAG, "Interrupted while waiting for device registration", e);
                if (callback != null) {
                    callback.onError(e);
                }
            }
        }).start();
    }
    
    /**
     * 设备ID回调接口
     */
    public interface DeviceIdCallback {
        void onDeviceIdReady(String deviceId, String ssid);
        void onError(Exception e);
    }
    
    /**
     * 检查是否已初始化
     */
    public boolean isInitialized() {
        return isInitialized;
    }
    
    /**
     * 获取当前配置
     */
    public AppLogConfig getConfig() {
        return config;
    }
    
    /**
     * 获取事件管理器
     */
    public AppLogEventManager getEventManager() {
        return AppLogEventManager.getInstance();
    }
    
    /**
     * 获取AB测试管理器
     */
    public AppLogABTestManager getABTestManager() {
        return AppLogABTestManager.getInstance();
    }
    
    /**
     * 快速初始化方法（使用默认配置）
     */
    public static void quickInit(Application application, String appId) {
        AppLogConfig config = AppLogConfig.createDefault(appId);
        getInstance().initialize(application, config);
    }
    
    /**
     * 快速初始化方法（开发环境）
     */
    public static void quickInitDev(Application application, String appId) {
        AppLogConfig config = AppLogConfig.createDevelopment(appId);
        getInstance().initialize(application, config);
    }
    
    /**
     * 快速初始化方法（生产环境）
     */
    public static void quickInitProd(Application application, String appId) {
        AppLogConfig config = AppLogConfig.createProduction(appId);
        getInstance().initialize(application, config);
    }
}
