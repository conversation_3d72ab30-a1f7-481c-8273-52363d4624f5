package com.bytedance.applog

import android.app.Application
import android.util.Log
import com.bytedance.util.Constant
import kotlinx.coroutines.*

/**
 * AppLog使用示例 - Kotlin版本
 * 展示如何使用Kotlin封装后的AppLog功能
 * 
 * <AUTHOR>
 */
object AppLogExample {
    
    private const val TAG = "AppLogExample"
    
    /**
     * 示例1：基础初始化
     */
    fun basicInitialization(application: Application) {
        // 最简单的初始化方式
        application.initAppLog(Constant.appId)
        
        // 或者使用扩展函数
        application.initAppLogDev(Constant.appId)  // 开发环境
        // application.initAppLogProd(Constant.appId) // 生产环境
    }
    
    /**
     * 示例2：DSL风格的自定义配置初始化
     */
    fun customInitialization(application: Application) {
        application.initAppLog(Constant.appId) {
            channel("custom-channel")
            logEnabled(true)
            abEnabled(true)
            encryptEnabled(false)
        }
        
        // 或者使用传统方式
        val config = appLogConfig(Constant.appId) {
            channel("custom-channel")
            logEnabled(true)
            abEnabled(true)
            encryptEnabled(false)
        }
        
        AppLogManager.initialize(application, config, object : AppLogManager.InitializationListener {
            override fun onInitSuccess() {
                Log.i(TAG, "AppLog初始化成功")
                setupAfterInit()
            }
            
            override fun onInitFailure(exception: Exception) {
                Log.e(TAG, "AppLog初始化失败", exception)
            }
        })
    }
    
    /**
     * 示例3：协程版本的初始化
     */
    suspend fun coroutineInitialization(application: Application) {
        val config = AppLogConfig.createDevelopment(Constant.appId)
        
        AppLogManager.initializeAsync(application, config)
            .onSuccess {
                Log.i(TAG, "协程初始化成功")
                setupAfterInit()
            }
            .onFailure { exception ->
                Log.e(TAG, "协程初始化失败", exception)
            }
    }
    
    /**
     * 示例4：事件上报 - 多种方式
     */
    fun trackingEvents(userId: String?) {
        // 方式1：使用工具类的便捷方法
        AppLogUtils.trackPageView(userId, "MacBook Pro", 20000.0, "white")
        AppLogUtils.trackSearch(userId, "电脑", "/file/computers/details")
        AppLogUtils.trackAddCart(userId, "MacBook Pro", 20000.0, "white")
        AppLogUtils.trackPay(userId, "MacBook Pro", 19500.0, "creditCard")
        
        // 方式2：使用DSL风格
        trackEvent("custom_event") {
            userId(userId)
            property("custom_prop1", "value1")
            property("custom_prop2", 123)
            commodityName("iPhone 14")
            price(8999.0)
            color("black")
        }
        
        // 方式3：使用扩展函数
        "MacBook Pro".trackAsPageView(userId, 20000.0, "silver")
        "电脑".trackAsSearch(userId, "/search/computers")
        
        // 方式4：条件事件追踪
        trackEventIf(userId != null, "user_action") {
            userId(userId)
            property("action_type", "purchase")
        }
        
        // 方式5：批量事件追踪
        trackEvents(
            "event1" to mapOf("prop1" to "value1"),
            "event2" to mapOf("prop2" to "value2"),
            "event3" to null
        )
    }
    
    /**
     * 示例5：用户属性设置 - 多种方式
     */
    fun setUserProperties(userId: String?) {
        // 方式1：使用便捷方法
        AppLogUtils.setUserProfile(userId, "张无忌", "男", 30, "乾坤大挪移")
        
        // 方式2：使用DSL风格
        setUserProfile(userId) {
            userName("张无忌")
            gender("男")
            age(30)
            skill("乾坤大挪移")
            property("vip_level", "gold")
            property("registration_date", "2023-01-01")
        }
        
        // 方式3：使用Map和扩展函数
        val userProps = mapOf(
            "user_name" to "张无忌",
            "user_gender" to "男",
            "user_age" to 30,
            "city" to "北京"
        )
        AppLogUtils.setUserProfile(userId, userProps)
    }
    
    /**
     * 示例6：AB测试使用 - 多种方式
     */
    fun setupABTests() {
        // 方式1：直接获取
        val buttonColor = AppLogUtils.getABConfig("button_color", "blue")
        val newFeatureEnabled = AppLogUtils.getABConfigBoolean("new_feature", false)
        val maxRetries = AppLogUtils.getABConfigInt("max_retries", 3)
        
        // 方式2：使用扩展函数
        val uiTheme = "ui_theme".asABConfig("light")
        val enableNewUI = "enable_new_ui".asABConfigBoolean(false)
        val retryCount = "retry_count".asABConfigInt(5)
        
        // 方式3：使用DSL风格
        abConfig("ab1") {
            defaultValue("default")
            listener(object : AppLogABTestManager.ABTestListener {
                override fun onABTestResult(experimentKey: String, experimentValue: String) {
                    Log.i(TAG, "AB测试结果: $experimentKey = $experimentValue")
                    handleABTestResult(experimentKey, experimentValue)
                }
                
                override fun onABTestError(experimentKey: String, error: String) {
                    Log.e(TAG, "AB测试错误: $experimentKey - $error")
                }
            })
        }.get()
        
        // 方式4：使用高阶函数
        withABTest("feature_flag", false) { enabled ->
            if (enabled) {
                enableNewFeature()
            }
        }
        
        // 方式5：使用常用实验
        if (AppLogABTestManager.CommonExperiments.isFeatureEnabled("new_ui")) {
            showNewUI()
        }
    }
    
    /**
     * 示例7：公共属性设置
     */
    fun setupCommonProperties() {
        // 方式1：传统方式
        val commonProps = mapOf(
            "app_version" to "2.0.0",
            "platform" to "android",
            "channel" to "google_play"
        )
        AppLogUtils.setCommonProperties(commonProps)
        
        // 方式2：使用扩展函数
        mapOf(
            "app_version" to "2.0.0",
            "platform" to "android",
            "build_type" to "debug"
        ).setAsCommonProperties()
    }
    
    /**
     * 示例8：设备信息获取 - 协程版本
     */
    suspend fun getDeviceInfo() {
        try {
            val (deviceId, ssid) = getDeviceInfoAsync()
            Log.i(TAG, "设备ID: $deviceId, SSID: $ssid")
        } catch (e: Exception) {
            Log.e(TAG, "获取设备信息失败", e)
        }
    }
    
    /**
     * 示例9：设备信息获取 - 回调版本
     */
    fun getDeviceInfoWithCallback() {
        AppLogManager.getDeviceIdAsync(object : AppLogManager.DeviceIdCallback {
            override fun onDeviceIdReady(deviceId: String, ssid: String) {
                Log.i(TAG, "设备ID: $deviceId, SSID: $ssid")
            }
            
            override fun onError(exception: Exception) {
                Log.e(TAG, "获取设备信息失败", exception)
            }
        })
    }
    
    /**
     * 示例10：ALink监听器设置
     */
    fun setupALinkListener() {
        AppLogManager.setALinkListener(object : AppLogManager.ALinkListener {
            override fun onALinkData(data: Map<String, String>?, error: Exception?) {
                when {
                    error != null -> Log.e(TAG, "ALink数据错误", error)
                    data != null -> {
                        Log.i(TAG, "ALink数据: $data")
                        // 处理ALink数据
                    }
                }
            }
            
            override fun onAttributionData(data: Map<String, String>?, error: Exception?) {
                when {
                    error != null -> Log.e(TAG, "归因数据错误", error)
                    data != null -> {
                        Log.i(TAG, "归因数据: $data")
                        // 处理归因数据
                    }
                }
            }
        })
    }
    
    /**
     * 示例11：用户生命周期管理
     */
    fun userLifecycleExample(userId: String?) {
        // 用户登录
        userId?.let { id ->
            id.setAsUserId()  // 使用扩展函数设置用户ID
            
            trackEvent("user_login") {
                userId(id)
                property("login_method", "email")
                property("login_time", System.currentTimeMillis())
            }
            
            // 设置用户属性
            setUserProfile(id) {
                userName("用户名")
                age(25)
                property("registration_date", "2023-01-01")
            }
        }
        
        // 用户登出
        trackEvent("user_logout") {
            userId(userId)
            property("logout_time", System.currentTimeMillis())
        }
    }
    
    /**
     * 示例12：完整的使用流程
     */
    suspend fun completeExample(application: Application, userId: String?) {
        // 1. 协程初始化
        coroutineInitialization(application)
        
        // 2. 设置用户ID
        userId?.setAsUserId()
        
        // 3. 设置用户属性
        setUserProperties(userId)
        
        // 4. 设置公共属性
        setupCommonProperties()
        
        // 5. 上报事件
        trackingEvents(userId)
        
        // 6. 获取设备信息
        getDeviceInfo()
        
        // 7. 设置ALink监听器
        setupALinkListener()
        
        // 8. AB测试
        setupABTests()
    }
    
    // 私有辅助方法
    private fun setupAfterInit() {
        Log.i(TAG, "初始化后设置完成")
    }
    
    private fun handleABTestResult(experimentKey: String, experimentValue: String) {
        when (experimentKey) {
            "ab1" -> when (experimentValue) {
                "1" -> Log.i(TAG, "实验组1的逻辑")
                "2" -> Log.i(TAG, "实验组2的逻辑")
                else -> Log.i(TAG, "对照组的逻辑")
            }
            "button_color" -> Log.i(TAG, "设置按钮颜色: $experimentValue")
            else -> Log.d(TAG, "未处理的实验: $experimentKey")
        }
    }
    
    private fun enableNewFeature() {
        Log.i(TAG, "启用新功能")
    }
    
    private fun showNewUI() {
        Log.i(TAG, "显示新UI")
    }
}
