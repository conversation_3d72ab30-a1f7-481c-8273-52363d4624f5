package com.bytedance.applog;

import com.bytedance.applog.UriConfig;
import com.bytedance.applog.util.UriConstants;

import java.util.Arrays;
import java.util.List;

/**
 * AppLog配置管理类
 * 统一管理所有AppLog相关的配置信息
 * 
 * <AUTHOR>
 */
public class AppLogConfig {
    
    // 默认配置
    private static final String DEFAULT_CHANNEL = "test-new";
    private static final String DEFAULT_DOMAIN = "https://gator.uba.ap-southeast-1.volces.com";
    private static final String DEFAULT_AB_DOMAIN = "https://tab.ab.ap-southeast-1.volces.com";
    private static final List<String> DEFAULT_H5_ALLOWLIST = Arrays.asList("*.bytedance.*", "*.pstatp.*");
    
    // 配置参数
    private String appId;
    private String channel;
    private String domain;
    private String abDomain;
    private boolean abEnabled;
    private boolean logEnabled;
    private boolean pickerEnabled;
    private boolean autoTrackEnabled;
    private boolean h5BridgeEnabled;
    private boolean h5CollectEnabled;
    private boolean autoTrackFragmentEnabled;
    private boolean encryptEnabled;
    private boolean autoStart;
    private boolean clipboardEnabled;
    private boolean deferredALinkEnabled;
    private List<String> h5BridgeAllowlist;
    private String userUniqueId;
    
    private AppLogConfig(Builder builder) {
        this.appId = builder.appId;
        this.channel = builder.channel;
        this.domain = builder.domain;
        this.abDomain = builder.abDomain;
        this.abEnabled = builder.abEnabled;
        this.logEnabled = builder.logEnabled;
        this.pickerEnabled = builder.pickerEnabled;
        this.autoTrackEnabled = builder.autoTrackEnabled;
        this.h5BridgeEnabled = builder.h5BridgeEnabled;
        this.h5CollectEnabled = builder.h5CollectEnabled;
        this.autoTrackFragmentEnabled = builder.autoTrackFragmentEnabled;
        this.encryptEnabled = builder.encryptEnabled;
        this.autoStart = builder.autoStart;
        this.clipboardEnabled = builder.clipboardEnabled;
        this.deferredALinkEnabled = builder.deferredALinkEnabled;
        this.h5BridgeAllowlist = builder.h5BridgeAllowlist;
        this.userUniqueId = builder.userUniqueId;
    }
    
    // Getter方法
    public String getAppId() { return appId; }
    public String getChannel() { return channel; }
    public String getDomain() { return domain; }
    public String getAbDomain() { return abDomain; }
    public boolean isAbEnabled() { return abEnabled; }
    public boolean isLogEnabled() { return logEnabled; }
    public boolean isPickerEnabled() { return pickerEnabled; }
    public boolean isAutoTrackEnabled() { return autoTrackEnabled; }
    public boolean isH5BridgeEnabled() { return h5BridgeEnabled; }
    public boolean isH5CollectEnabled() { return h5CollectEnabled; }
    public boolean isAutoTrackFragmentEnabled() { return autoTrackFragmentEnabled; }
    public boolean isEncryptEnabled() { return encryptEnabled; }
    public boolean isAutoStart() { return autoStart; }
    public boolean isClipboardEnabled() { return clipboardEnabled; }
    public boolean isDeferredALinkEnabled() { return deferredALinkEnabled; }
    public List<String> getH5BridgeAllowlist() { return h5BridgeAllowlist; }
    public String getUserUniqueId() { return userUniqueId; }
    
    /**
     * 创建UriConfig
     */
    public UriConfig createUriConfig() {
        UriConfig uriConfig = UriConfig.createByDomain(domain, null);
        if (abDomain != null) {
            uriConfig.setAbUri(abDomain + UriConfig.PATH_AB);
        }
        return uriConfig;
    }
    
    /**
     * Builder模式构建配置
     */
    public static class Builder {
        private String appId;
        private String channel = DEFAULT_CHANNEL;
        private String domain = DEFAULT_DOMAIN;
        private String abDomain = DEFAULT_AB_DOMAIN;
        private boolean abEnabled = true;
        private boolean logEnabled = true;
        private boolean pickerEnabled = true;
        private boolean autoTrackEnabled = true;
        private boolean h5BridgeEnabled = true;
        private boolean h5CollectEnabled = true;
        private boolean autoTrackFragmentEnabled = true;
        private boolean encryptEnabled = false;
        private boolean autoStart = true;
        private boolean clipboardEnabled = true;
        private boolean deferredALinkEnabled = true;
        private List<String> h5BridgeAllowlist = DEFAULT_H5_ALLOWLIST;
        private String userUniqueId;
        
        public Builder(String appId) {
            this.appId = appId;
        }
        
        public Builder channel(String channel) {
            this.channel = channel;
            return this;
        }
        
        public Builder domain(String domain) {
            this.domain = domain;
            return this;
        }
        
        public Builder abDomain(String abDomain) {
            this.abDomain = abDomain;
            return this;
        }
        
        public Builder abEnabled(boolean abEnabled) {
            this.abEnabled = abEnabled;
            return this;
        }
        
        public Builder logEnabled(boolean logEnabled) {
            this.logEnabled = logEnabled;
            return this;
        }
        
        public Builder pickerEnabled(boolean pickerEnabled) {
            this.pickerEnabled = pickerEnabled;
            return this;
        }
        
        public Builder autoTrackEnabled(boolean autoTrackEnabled) {
            this.autoTrackEnabled = autoTrackEnabled;
            return this;
        }
        
        public Builder h5BridgeEnabled(boolean h5BridgeEnabled) {
            this.h5BridgeEnabled = h5BridgeEnabled;
            return this;
        }
        
        public Builder h5CollectEnabled(boolean h5CollectEnabled) {
            this.h5CollectEnabled = h5CollectEnabled;
            return this;
        }
        
        public Builder autoTrackFragmentEnabled(boolean autoTrackFragmentEnabled) {
            this.autoTrackFragmentEnabled = autoTrackFragmentEnabled;
            return this;
        }
        
        public Builder encryptEnabled(boolean encryptEnabled) {
            this.encryptEnabled = encryptEnabled;
            return this;
        }
        
        public Builder autoStart(boolean autoStart) {
            this.autoStart = autoStart;
            return this;
        }
        
        public Builder clipboardEnabled(boolean clipboardEnabled) {
            this.clipboardEnabled = clipboardEnabled;
            return this;
        }
        
        public Builder deferredALinkEnabled(boolean deferredALinkEnabled) {
            this.deferredALinkEnabled = deferredALinkEnabled;
            return this;
        }
        
        public Builder h5BridgeAllowlist(List<String> h5BridgeAllowlist) {
            this.h5BridgeAllowlist = h5BridgeAllowlist;
            return this;
        }
        
        public Builder userUniqueId(String userUniqueId) {
            this.userUniqueId = userUniqueId;
            return this;
        }
        
        public AppLogConfig build() {
            return new AppLogConfig(this);
        }
    }
    
    /**
     * 创建默认配置
     */
    public static AppLogConfig createDefault(String appId) {
        return new Builder(appId).build();
    }
    
    /**
     * 创建生产环境配置
     */
    public static AppLogConfig createProduction(String appId) {
        return new Builder(appId)
                .logEnabled(false)
                .encryptEnabled(true)
                .build();
    }
    
    /**
     * 创建开发环境配置
     */
    public static AppLogConfig createDevelopment(String appId) {
        return new Builder(appId)
                .logEnabled(true)
                .encryptEnabled(false)
                .build();
    }
}
