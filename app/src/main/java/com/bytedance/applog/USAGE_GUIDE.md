# AppLog 封装库完整使用指南

## 概述

这个封装库对火山引擎 AppLog SDK 进行了全面的封装和优化，提供了更简洁、易用和可扩展的 API。主要解决了原始代码中初始化复杂、事件上报重复、配置分散等问题。

## 快速开始

### 1. 在 Application 中初始化

```java
public class MyApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        
        // 最简单的方式
        AppLogUtils.init(this, "your_app_id");
        
        // 或者根据环境选择
        if (BuildConfig.DEBUG) {
            AppLogUtils.initDev(this, "your_app_id");  // 开发环境
        } else {
            AppLogUtils.initProd(this, "your_app_id"); // 生产环境
        }
    }
}
```

### 2. 基础事件上报

```java
// 设置用户ID
AppLogUtils.setUserId("user123");

// 浏览商品
AppLogUtils.trackPageView("user123", "iPhone 14", 8999, "黑色");

// 搜索
AppLogUtils.trackSearch("user123", "手机", "/search/phone");

// 添加购物车
AppLogUtils.trackAddCart("user123", "iPhone 14", 8999, "黑色");

// 支付
AppLogUtils.trackPay("user123", "iPhone 14", 8999, "支付宝");
```

### 3. AB测试使用

```java
// 获取AB配置
String buttonColor = AppLogUtils.getABConfig("button_color", "blue");
boolean newFeature = AppLogUtils.getABConfigBoolean("new_feature", false);

// 根据配置调整UI
if ("red".equals(buttonColor)) {
    button.setBackgroundColor(Color.RED);
}

if (newFeature) {
    enableNewFeature();
}
```

## 详细功能说明

### 配置管理 (AppLogConfig)

#### 预设配置
```java
// 默认配置
AppLogConfig defaultConfig = AppLogConfig.createDefault("app_id");

// 开发环境配置（启用日志，关闭加密）
AppLogConfig devConfig = AppLogConfig.createDevelopment("app_id");

// 生产环境配置（关闭日志，启用加密）
AppLogConfig prodConfig = AppLogConfig.createProduction("app_id");
```

#### 自定义配置
```java
AppLogConfig config = new AppLogConfig.Builder("your_app_id")
    .channel("custom_channel")                    // 渠道
    .domain("https://your-domain.com")           // 上报域名
    .abDomain("https://your-ab-domain.com")      // AB测试域名
    .logEnabled(true)                            // 启用日志
    .abEnabled(true)                             // 启用AB测试
    .encryptEnabled(false)                       // 加密开关
    .pickerEnabled(true)                         // 圈选功能
    .autoTrackEnabled(true)                      // 自动埋点
    .h5BridgeEnabled(true)                       // H5桥接
    .build();

// 使用自定义配置初始化
AppLogManager.getInstance().initialize(this, config);
```

### 事件管理 (AppLogEventManager)

#### 便捷方法
```java
// 商品相关事件
AppLogUtils.trackPageView(userId, "商品名", 价格, "颜色");
AppLogUtils.trackAddCart(userId, "商品名", 价格, "颜色");
AppLogUtils.trackPay(userId, "商品名", 价格, "支付方式");

// 用户行为事件
AppLogUtils.trackSearch(userId, "搜索词", "结果页面");
AppLogUtils.trackLogin(userId, "登录方式");
AppLogUtils.trackLogout(userId);
AppLogUtils.trackRegister(userId, "注册方式");
```

#### 构建器模式
```java
// 自定义事件
AppLogUtils.event("custom_event")
    .userId("user123")
    .property("key1", "value1")
    .property("key2", 123)
    .property("key3", true)
    .track();

// 商品事件
AppLogUtils.event("product_view")
    .userId("user123")
    .commodityName("iPhone 14")
    .price(8999)
    .color("黑色")
    .property("category", "手机")
    .property("brand", "Apple")
    .track();
```

#### 用户属性设置
```java
// 便捷方法
AppLogUtils.setUserProfile("user123", "张三", "男", 25, "开发工程师");

// 构建器模式
AppLogUtils.userProfile("user123")
    .userName("张三")
    .gender("男")
    .age(25)
    .skill("开发工程师")
    .property("city", "北京")
    .property("education", "本科")
    .set();

// Map方式
Map<String, Object> userProps = new HashMap<>();
userProps.put("user_name", "张三");
userProps.put("user_age", 25);
AppLogUtils.setUserProfile("user123", userProps);
```

### AB测试管理 (AppLogABTestManager)

#### 基础用法
```java
// 获取不同类型的配置
String stringValue = AppLogUtils.getABConfig("experiment_key", "default");
boolean boolValue = AppLogUtils.getABConfigBoolean("feature_flag", false);
int intValue = AppLogUtils.getABConfigInt("retry_count", 3);
double doubleValue = AppLogUtils.getABConfigDouble("price_factor", 1.0);

// 检查是否在实验组
boolean inExperiment = AppLogUtils.isInExperimentGroup("ab_test", "group_a");
```

#### 使用监听器
```java
AppLogABTestManager.config("ui_theme")
    .defaultValue("light")
    .listener(new AppLogABTestManager.ABTestListener() {
        @Override
        public void onABTestResult(String key, String value) {
            Log.i("AB", "实验结果: " + key + " = " + value);
            applyTheme(value);
        }
        
        @Override
        public void onABTestError(String key, String error) {
            Log.e("AB", "实验错误: " + error);
            applyTheme("light"); // 使用默认值
        }
    })
    .get();
```

#### 常用实验场景
```java
// 功能开关
if (AppLogABTestManager.CommonExperiments.isFeatureEnabled("new_ui")) {
    showNewUI();
}

// UI版本
String uiVersion = AppLogABTestManager.CommonExperiments.getUIVersion();
loadUI(uiVersion);

// 按钮颜色
String buttonColor = AppLogABTestManager.CommonExperiments.getButtonColor();
setButtonColor(buttonColor);
```

### 设备信息获取

#### 异步获取（推荐）
```java
AppLogManager.getInstance().getDeviceIdAsync(new AppLogManager.DeviceIdCallback() {
    @Override
    public void onDeviceIdReady(String deviceId, String ssid) {
        Log.i("Device", "设备ID: " + deviceId);
        Log.i("Device", "SSID: " + ssid);
        // 使用设备信息
    }
    
    @Override
    public void onError(Exception e) {
        Log.e("Device", "获取失败", e);
    }
});
```

#### 同步获取
```java
// 注意：需要确保SDK已初始化完成
String deviceId = AppLogUtils.getDeviceId();
String ssid = AppLogUtils.getSSID();
```

### 公共属性管理

```java
// 设置公共属性
Map<String, Object> commonProps = new HashMap<>();
commonProps.put("app_version", "1.0.0");
commonProps.put("platform", "android");
commonProps.put("channel", "google_play");
AppLogUtils.setCommonProperties(commonProps);

// 清除公共属性
AppLogEventManager.getInstance().clearCommonProperties();
```

### 广告监测 (ALink)

```java
AppLogManager.getInstance().setALinkListener(new AppLogManager.ALinkListener() {
    @Override
    public void onALinkData(Map<String, String> data, Exception error) {
        if (error == null && data != null) {
            // 处理ALink数据
            Log.i("ALink", "数据: " + data.toString());
        }
    }
    
    @Override
    public void onAttributionData(Map<String, String> data, Exception error) {
        if (error == null && data != null) {
            // 处理归因数据
            Log.i("Attribution", "数据: " + data.toString());
        }
    }
});
```

## 高级用法

### 自定义初始化监听
```java
AppLogConfig config = AppLogConfig.createDefault("app_id");
AppLogManager.getInstance().initialize(this, config, new AppLogManager.InitializationListener() {
    @Override
    public void onInitSuccess() {
        Log.i("AppLog", "初始化成功");
        // 初始化成功后的操作
        setupABTests();
        preloadConfigs();
    }
    
    @Override
    public void onInitFailure(Exception e) {
        Log.e("AppLog", "初始化失败", e);
        // 错误处理
    }
});
```

### 预加载AB配置
```java
String[] keys = {"feature_a", "feature_b", "ui_theme"};
String[] defaults = {"false", "false", "light"};
AppLogABTestManager.getInstance().preloadABConfigs(keys, defaults);
```

### 缓存管理
```java
// 获取缓存的AB配置
String cachedValue = AppLogABTestManager.getInstance().getCachedABConfig("experiment_key");

// 获取所有缓存的配置
Map<String, String> allCached = AppLogABTestManager.getInstance().getAllCachedABConfigs();

// 清除缓存
AppLogABTestManager.getInstance().clearABConfigCache();
```

## 最佳实践

### 1. 初始化时机
```java
public class MyApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        // 在Application的onCreate中初始化
        initAppLog();
    }
    
    private void initAppLog() {
        if (BuildConfig.DEBUG) {
            AppLogUtils.initDev(this, APP_ID);
        } else {
            AppLogUtils.initProd(this, APP_ID);
        }
    }
}
```

### 2. 用户生命周期管理
```java
// 用户登录时
public void onUserLogin(String userId) {
    AppLogUtils.setUserId(userId);
    AppLogUtils.trackLogin(userId, "email");
    
    // 设置用户属性
    AppLogUtils.setUserProfile(userId, userName, gender, age, profession);
}

// 用户登出时
public void onUserLogout(String userId) {
    AppLogUtils.trackLogout(userId);
    // 注意：不要清除用户ID，保持匿名追踪
}
```

### 3. 页面生命周期追踪
```java
public class BaseActivity extends AppCompatActivity {
    @Override
    protected void onResume() {
        super.onResume();
        AppLogUtils.event("page_enter")
            .userId(getCurrentUserId())
            .property("page_name", getClass().getSimpleName())
            .property("enter_time", System.currentTimeMillis())
            .track();
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        AppLogUtils.event("page_leave")
            .userId(getCurrentUserId())
            .property("page_name", getClass().getSimpleName())
            .property("leave_time", System.currentTimeMillis())
            .track();
    }
}
```

### 4. 错误处理
```java
// 使用try-catch包装关键操作
try {
    AppLogUtils.trackPay(userId, productName, price, payMethod);
} catch (Exception e) {
    Log.e("AppLog", "支付事件上报失败", e);
    // 不要让埋点错误影响业务逻辑
}
```

### 5. 性能优化
```java
// 避免在主线程进行复杂操作
new Thread(() -> {
    // 批量设置用户属性
    Map<String, Object> properties = buildUserProperties();
    AppLogUtils.setUserProfile(userId, properties);
}).start();
```

## 迁移指南

### 从原始AppLog迁移

#### 原始代码
```java
// 初始化
InitConfig config = new InitConfig(appId, "channel");
// ... 大量配置代码
AppLog.init(this, config);

// 事件上报
JSONObject params = new JSONObject();
params.put("key", "value");
AppLog.onEventV3("event", params);
```

#### 迁移后
```java
// 初始化
AppLogUtils.init(this, appId);

// 事件上报
AppLogUtils.event("event")
    .property("key", "value")
    .track();
```

## 常见问题

### Q: 如何确保初始化完成？
A: 使用初始化监听器或检查初始化状态
```java
if (AppLogManager.getInstance().isInitialized()) {
    // 已初始化，可以安全使用
}
```

### Q: 设备ID为空怎么办？
A: 使用异步方式获取，确保SDK注册完成
```java
AppLogManager.getInstance().getDeviceIdAsync(callback);
```

### Q: AB测试配置不生效？
A: 确保AB测试已启用，且实验已正确配置
```java
AppLogConfig config = new AppLogConfig.Builder(appId)
    .abEnabled(true)  // 确保启用AB测试
    .build();
```

### Q: 如何调试事件上报？
A: 启用日志模式查看详细输出
```java
AppLogUtils.initDev(this, appId);  // 开发环境会启用日志
```

这个封装库大大简化了AppLog的使用，提高了开发效率和代码质量。建议在项目中逐步迁移到新的API。
