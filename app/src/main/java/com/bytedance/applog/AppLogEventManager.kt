package com.bytedance.applog

import android.util.Log
import com.bytedance.applog.AppLog
import org.json.JSONObject

/**
 * AppLog事件管理器 - Kotlin版本
 * 统一管理所有事件上报逻辑，提供简化的API
 * 
 * <AUTHOR>
 */
object AppLogEventManager {
    
    private const val TAG = "AppLogEventManager"
    
    // 事件名称常量
    object Events {
        const val PAGE_VIEW = "pageView"
        const val SEARCH = "search"
        const val ADD_CART = "addcart"
        const val PAY = "pay"
        const val LOGIN = "login"
        const val LOGOUT = "logout"
        const val REGISTER = "register"
    }
    
    // 事件属性常量
    object Properties {
        const val COMMODITY_NAME = "commodity_name"
        const val PRICE = "price"
        const val COLOR = "color"
        const val SEARCH_KEYWORDS = "search_keywords"
        const val RESULT_DETAILS = "result_details"
        const val PAY_METHOD = "pay_method"
        const val USER_NAME = "user_name"
        const val USER_GENDER = "user_gender"
        const val USER_AGE = "user_age"
        const val USER_SKILL = "user_skill"
    }
    
    /**
     * 设置用户ID
     */
    fun setUserId(userId: String?) {
        userId?.takeIf { it.isNotEmpty() }?.let {
            AppLog.setUserUniqueID(it)
            Log.d(TAG, "User ID set: $it")
        }
    }
    
    /**
     * 上报事件（基础方法）
     */
    fun trackEvent(
        eventName: String,
        properties: Map<String, Any>? = null,
        userId: String? = null
    ) {
        try {
            userId?.let { setUserId(it) }
            
            val paramsObj = JSONObject().apply {
                properties?.forEach { (key, value) ->
                    put(key, value)
                }
            }
            
            AppLog.onEventV3(eventName, paramsObj)
            Log.d(TAG, "Event tracked: $eventName with properties: $paramsObj")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error tracking event: $eventName", e)
        }
    }
    
    /**
     * 浏览商品事件
     */
    fun trackPageView(
        userId: String?,
        commodityName: String,
        price: Double,
        color: String
    ) {
        val properties = mapOf(
            Properties.COMMODITY_NAME to commodityName,
            Properties.PRICE to price,
            Properties.COLOR to color
        )
        trackEvent(Events.PAGE_VIEW, properties, userId)
    }
    
    /**
     * 搜索事件
     */
    fun trackSearch(
        userId: String?,
        keywords: String,
        resultDetails: String
    ) {
        val properties = mapOf(
            Properties.SEARCH_KEYWORDS to keywords,
            Properties.RESULT_DETAILS to resultDetails
        )
        trackEvent(Events.SEARCH, properties, userId)
    }
    
    /**
     * 添加购物车事件
     */
    fun trackAddCart(
        userId: String?,
        commodityName: String,
        price: Double,
        color: String
    ) {
        val properties = mapOf(
            Properties.COMMODITY_NAME to commodityName,
            Properties.PRICE to price,
            Properties.COLOR to color
        )
        trackEvent(Events.ADD_CART, properties, userId)
    }
    
    /**
     * 支付事件
     */
    fun trackPay(
        userId: String?,
        commodityName: String,
        price: Double,
        payMethod: String
    ) {
        if (userId == null) {
            Log.w(TAG, "Pay event requires user ID")
            return
        }
        
        val properties = mapOf(
            Properties.COMMODITY_NAME to commodityName,
            Properties.PRICE to price,
            Properties.PAY_METHOD to payMethod
        )
        trackEvent(Events.PAY, properties, userId)
    }
    
    /**
     * 登录事件
     */
    fun trackLogin(userId: String?, loginMethod: String) {
        val properties = mapOf("login_method" to loginMethod)
        trackEvent(Events.LOGIN, properties, userId)
    }
    
    /**
     * 登出事件
     */
    fun trackLogout(userId: String?) {
        trackEvent(Events.LOGOUT, null, userId)
    }
    
    /**
     * 注册事件
     */
    fun trackRegister(userId: String?, registerMethod: String) {
        val properties = mapOf("register_method" to registerMethod)
        trackEvent(Events.REGISTER, properties, userId)
    }
    
    /**
     * 设置用户属性
     */
    fun setUserProfile(userId: String?, userProperties: Map<String, Any>) {
        if (userId == null) {
            Log.w(TAG, "User profile requires user ID")
            return
        }
        
        try {
            setUserId(userId)
            
            val paramsObj = JSONObject().apply {
                userProperties.forEach { (key, value) ->
                    put(key, value)
                }
            }
            
            AppLog.profileSet(paramsObj)
            Log.d(TAG, "User profile set for user: $userId with properties: $paramsObj")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error setting user profile for user: $userId", e)
        }
    }
    
    /**
     * 设置用户属性（便捷方法）
     */
    fun setUserProfile(
        userId: String?,
        userName: String,
        gender: String,
        age: Int,
        skill: String
    ) {
        val properties = mapOf(
            Properties.USER_NAME to userName,
            Properties.USER_GENDER to gender,
            Properties.USER_AGE to age,
            Properties.USER_SKILL to skill
        )
        setUserProfile(userId, properties)
    }
    
    /**
     * 设置事件公共属性
     */
    fun setCommonProperties(commonProperties: Map<String, Any>) {
        AppLog.setHeaderInfo(HashMap(commonProperties))
        Log.d(TAG, "Common properties set: $commonProperties")
    }
    
    /**
     * 清除事件公共属性
     */
    fun clearCommonProperties() {
        AppLog.setHeaderInfo(HashMap<String, Any>())
        Log.d(TAG, "Common properties cleared")
    }
}

/**
 * 事件构建器 - 支持DSL风格
 */
class EventBuilder(private val eventName: String) {
    private val properties = mutableMapOf<String, Any>()
    private var userId: String? = null
    
    fun userId(userId: String?) = apply { this.userId = userId }
    
    fun property(key: String, value: Any) = apply { properties[key] = value }
    
    fun commodityName(name: String) = property(AppLogEventManager.Properties.COMMODITY_NAME, name)
    fun price(price: Double) = property(AppLogEventManager.Properties.PRICE, price)
    fun color(color: String) = property(AppLogEventManager.Properties.COLOR, color)
    fun searchKeywords(keywords: String) = property(AppLogEventManager.Properties.SEARCH_KEYWORDS, keywords)
    fun resultDetails(details: String) = property(AppLogEventManager.Properties.RESULT_DETAILS, details)
    fun payMethod(method: String) = property(AppLogEventManager.Properties.PAY_METHOD, method)
    
    fun track() {
        AppLogEventManager.trackEvent(eventName, properties, userId)
    }
}

/**
 * 用户属性构建器 - 支持DSL风格
 */
class UserProfileBuilder(private val userId: String?) {
    private val properties = mutableMapOf<String, Any>()
    
    fun property(key: String, value: Any) = apply { properties[key] = value }
    
    fun userName(name: String) = property(AppLogEventManager.Properties.USER_NAME, name)
    fun gender(gender: String) = property(AppLogEventManager.Properties.USER_GENDER, gender)
    fun age(age: Int) = property(AppLogEventManager.Properties.USER_AGE, age)
    fun skill(skill: String) = property(AppLogEventManager.Properties.USER_SKILL, skill)
    
    fun set() {
        AppLogEventManager.setUserProfile(userId, properties)
    }
}
