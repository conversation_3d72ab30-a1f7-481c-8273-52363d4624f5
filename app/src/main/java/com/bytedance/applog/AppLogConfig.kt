package com.bytedance.applog

import com.bytedance.applog.UriConfig

/**
 * AppLog配置管理类 - Kotlin版本
 * 统一管理所有AppLog相关的配置信息
 * 
 * <AUTHOR>
 */
data class AppLogConfig(
    val appId: String,
    val channel: String = DEFAULT_CHANNEL,
    val domain: String = DEFAULT_DOMAIN,
    val abDomain: String = DEFAULT_AB_DOMAIN,
    val abEnabled: Boolean = true,
    val logEnabled: Boolean = true,
    val pickerEnabled: Boolean = true,
    val autoTrackEnabled: Boolean = true,
    val h5BridgeEnabled: Boolean = true,
    val h5CollectEnabled: Boolean = true,
    val autoTrackFragmentEnabled: Boolean = true,
    val encryptEnabled: Boolean = false,
    val autoStart: Boolean = true,
    val clipboardEnabled: Boolean = true,
    val deferredALinkEnabled: Boolean = true,
    val h5BridgeAllowlist: List<String> = DEFAULT_H5_ALLOWLIST,
    val userUniqueId: String? = null
) {
    
    companion object {
        // 默认配置常量
        private const val DEFAULT_CHANNEL = "test-new"
        private const val DEFAULT_DOMAIN = "https://gator.uba.ap-southeast-1.volces.com"
        private const val DEFAULT_AB_DOMAIN = "https://tab.ab.ap-southeast-1.volces.com"
        private val DEFAULT_H5_ALLOWLIST = listOf("*.bytedance.*", "*.pstatp.*")
        
        /**
         * 创建默认配置
         */
        fun createDefault(appId: String) = AppLogConfig(appId = appId)
        
        /**
         * 创建生产环境配置
         */
        fun createProduction(appId: String) = AppLogConfig(
            appId = appId,
            logEnabled = false,
            encryptEnabled = true
        )
        
        /**
         * 创建开发环境配置
         */
        fun createDevelopment(appId: String) = AppLogConfig(
            appId = appId,
            logEnabled = true,
            encryptEnabled = false
        )
    }
    
    /**
     * 创建UriConfig
     */
    fun createUriConfig(): UriConfig {
        val uriConfig = UriConfig.createByDomain(domain, null)
        uriConfig.setAbUri("$abDomain${UriConfig.PATH_AB}")
        return uriConfig
    }
    
    /**
     * Builder类 - 支持DSL风格构建
     */
    class Builder(private val appId: String) {
        private var channel: String = DEFAULT_CHANNEL
        private var domain: String = DEFAULT_DOMAIN
        private var abDomain: String = DEFAULT_AB_DOMAIN
        private var abEnabled: Boolean = true
        private var logEnabled: Boolean = true
        private var pickerEnabled: Boolean = true
        private var autoTrackEnabled: Boolean = true
        private var h5BridgeEnabled: Boolean = true
        private var h5CollectEnabled: Boolean = true
        private var autoTrackFragmentEnabled: Boolean = true
        private var encryptEnabled: Boolean = false
        private var autoStart: Boolean = true
        private var clipboardEnabled: Boolean = true
        private var deferredALinkEnabled: Boolean = true
        private var h5BridgeAllowlist: List<String> = DEFAULT_H5_ALLOWLIST
        private var userUniqueId: String? = null
        
        fun channel(channel: String) = apply { this.channel = channel }
        fun domain(domain: String) = apply { this.domain = domain }
        fun abDomain(abDomain: String) = apply { this.abDomain = abDomain }
        fun abEnabled(enabled: Boolean) = apply { this.abEnabled = enabled }
        fun logEnabled(enabled: Boolean) = apply { this.logEnabled = enabled }
        fun pickerEnabled(enabled: Boolean) = apply { this.pickerEnabled = enabled }
        fun autoTrackEnabled(enabled: Boolean) = apply { this.autoTrackEnabled = enabled }
        fun h5BridgeEnabled(enabled: Boolean) = apply { this.h5BridgeEnabled = enabled }
        fun h5CollectEnabled(enabled: Boolean) = apply { this.h5CollectEnabled = enabled }
        fun autoTrackFragmentEnabled(enabled: Boolean) = apply { this.autoTrackFragmentEnabled = enabled }
        fun encryptEnabled(enabled: Boolean) = apply { this.encryptEnabled = enabled }
        fun autoStart(enabled: Boolean) = apply { this.autoStart = enabled }
        fun clipboardEnabled(enabled: Boolean) = apply { this.clipboardEnabled = enabled }
        fun deferredALinkEnabled(enabled: Boolean) = apply { this.deferredALinkEnabled = enabled }
        fun h5BridgeAllowlist(allowlist: List<String>) = apply { this.h5BridgeAllowlist = allowlist }
        fun userUniqueId(id: String?) = apply { this.userUniqueId = id }
        
        fun build() = AppLogConfig(
            appId = appId,
            channel = channel,
            domain = domain,
            abDomain = abDomain,
            abEnabled = abEnabled,
            logEnabled = logEnabled,
            pickerEnabled = pickerEnabled,
            autoTrackEnabled = autoTrackEnabled,
            h5BridgeEnabled = h5BridgeEnabled,
            h5CollectEnabled = h5CollectEnabled,
            autoTrackFragmentEnabled = autoTrackFragmentEnabled,
            encryptEnabled = encryptEnabled,
            autoStart = autoStart,
            clipboardEnabled = clipboardEnabled,
            deferredALinkEnabled = deferredALinkEnabled,
            h5BridgeAllowlist = h5BridgeAllowlist,
            userUniqueId = userUniqueId
        )
    }
}

/**
 * DSL风格的配置构建函数
 */
inline fun appLogConfig(appId: String, block: AppLogConfig.Builder.() -> Unit): AppLogConfig {
    return AppLogConfig.Builder(appId).apply(block).build()
}
