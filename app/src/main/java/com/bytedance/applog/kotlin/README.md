# AppLog Kotlin 封装库使用指南

这是火山引擎 AppLog SDK 的 Kotlin 封装版本，充分利用了 Kotlin 的语言特性，提供了更简洁、类型安全和现代化的 API。

## 🚀 Kotlin 特性亮点

- **DSL 风格 API** - 更直观的配置和调用方式
- **扩展函数** - 为现有类型添加便捷方法
- **协程支持** - 异步操作更简洁优雅
- **空值安全** - 编译时避免 NullPointerException
- **数据类** - 简化配置管理
- **对象声明** - 更简洁的单例模式
- **高阶函数** - 函数式编程支持
- **内联函数** - 零开销抽象

## 📦 依赖配置

确保在 `app/build.gradle` 中添加 Kotlin 支持：

```gradle
plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.10"
    implementation "androidx.core:core-ktx:1.9.0"
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
}
```

## 🎯 快速开始

### 1. 初始化 - 多种方式

```kotlin
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        
        // 方式1：最简单的初始化
        initAppLog("your_app_id")
        
        // 方式2：环境区分
        if (BuildConfig.DEBUG) {
            initAppLogDev("your_app_id")
        } else {
            initAppLogProd("your_app_id")
        }
        
        // 方式3：DSL 风格自定义配置
        initAppLog("your_app_id") {
            channel("custom-channel")
            logEnabled(true)
            abEnabled(true)
            encryptEnabled(false)
        }
        
        // 方式4：协程版本
        lifecycleScope.launch {
            val config = appLogConfig("your_app_id") {
                logEnabled(true)
                abEnabled(true)
            }
            
            AppLogManager.initializeAsync(this@MyApplication, config)
                .onSuccess { Log.i("AppLog", "初始化成功") }
                .onFailure { Log.e("AppLog", "初始化失败", it) }
        }
    }
}
```

### 2. 事件上报 - DSL 风格

```kotlin
// 方式1：DSL 风格事件追踪
trackEvent("page_view") {
    userId("user123")
    commodityName("iPhone 14")
    price(8999.0)
    color("黑色")
    property("category", "手机")
    property("brand", "Apple")
}

// 方式2：扩展函数
"iPhone 14".trackAsPageView("user123", 8999.0, "黑色")
"手机".trackAsSearch("user123", "/search/phone")

// 方式3：条件事件追踪
trackEventIf(userId != null, "user_action") {
    userId(userId)
    property("action_type", "purchase")
}

// 方式4：批量事件追踪
trackEvents(
    "event1" to mapOf("prop1" to "value1"),
    "event2" to mapOf("prop2" to "value2"),
    "event3" to null
)
```

### 3. 用户属性 - DSL 风格

```kotlin
// DSL 风格设置用户属性
setUserProfile("user123") {
    userName("张三")
    gender("男")
    age(25)
    skill("Kotlin开发")
    property("city", "北京")
    property("vip_level", "gold")
    property("interests", listOf("编程", "旅游"))
}

// 扩展函数设置用户ID
"user123".setAsUserId()

// Map 扩展函数设置公共属性
mapOf(
    "app_version" to "2.0.0",
    "platform" to "android"
).setAsCommonProperties()
```

## 🧪 AB 测试 - 多种 Kotlin 用法

### 1. 扩展函数方式

```kotlin
val buttonColor = "button_color".asABConfig("blue")
val newFeatureEnabled = "new_feature".asABConfigBoolean(false)
val maxRetries = "max_retries".asABConfigInt(3)
```

### 2. 高阶函数方式

```kotlin
withABTest("ui_theme", "light") { theme ->
    when (theme) {
        "dark" -> applyDarkTheme()
        "light" -> applyLightTheme()
        else -> applyDefaultTheme()
    }
}
```

### 3. DSL 风格

```kotlin
abConfig("feature_flag") {
    defaultValue(false)
    listener(object : AppLogABTestManager.ABTestListener {
        override fun onABTestResult(key: String, value: String) {
            Log.i("AB", "实验结果: $key = $value")
        }
        
        override fun onABTestError(key: String, error: String) {
            Log.e("AB", "实验错误: $error")
        }
    })
}.getBoolean()
```

### 4. 内联函数简化

```kotlin
// 简化的AB测试DSL
abTest("feature_enabled", false) { enabled ->
    if (enabled) enableNewFeature()
}
```

## ⚡ 协程支持

### 1. 异步初始化

```kotlin
suspend fun initializeAppLog() {
    val config = AppLogConfig.createDevelopment("app_id")
    
    AppLogManager.initializeAsync(application, config)
        .onSuccess { 
            Log.i("AppLog", "初始化成功")
            setupAfterInit()
        }
        .onFailure { exception ->
            Log.e("AppLog", "初始化失败", exception)
        }
}
```

### 2. 异步获取设备信息

```kotlin
// 协程版本
suspend fun getDeviceInfo() {
    try {
        val (deviceId, ssid) = getDeviceInfoAsync()
        Log.i("Device", "设备ID: $deviceId, SSID: $ssid")
    } catch (e: Exception) {
        Log.e("Device", "获取失败", e)
    }
}

// 在 Activity 中使用
class MainActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        lifecycleScope.launch {
            getDeviceInfo()
        }
    }
}
```

## 🛡️ 空值安全

```kotlin
// 安全的用户操作
userId?.let { id ->
    id.setAsUserId()
    
    trackEvent("user_login") {
        userId(id)
        property("login_method", "email")
    }
    
    setUserProfile(id) {
        userName("用户名")
        age(25)
    }
} ?: Log.w("AppLog", "用户未登录")

// 安全的AB测试
val theme = "ui_theme".asABConfig("light")
when (theme) {
    "dark" -> applyDarkTheme()
    "light" -> applyLightTheme()
    else -> applyDefaultTheme()
}
```

## 🔧 扩展函数大全

### Application 扩展

```kotlin
// 初始化扩展
application.initAppLog("app_id")
application.initAppLogDev("app_id")
application.initAppLogProd("app_id")

// DSL 配置扩展
application.initAppLog("app_id") {
    channel("custom")
    logEnabled(true)
}
```

### String 扩展

```kotlin
// 用户ID扩展
"user123".setAsUserId()

// 事件追踪扩展
"商品名".trackAsPageView("user123", 999.0, "红色")
"搜索词".trackAsSearch("user123", "/search/result")

// AB配置扩展
"experiment_key".asABConfig("default")
"feature_flag".asABConfigBoolean(false)
"retry_count".asABConfigInt(3)
```

### Map 扩展

```kotlin
// 公共属性扩展
mapOf(
    "version" to "1.0",
    "platform" to "android"
).setAsCommonProperties()
```

## 🎨 DSL 配置示例

### 完整的配置 DSL

```kotlin
val config = appLogConfig("your_app_id") {
    channel("production")
    domain("https://your-domain.com")
    abDomain("https://your-ab-domain.com")
    logEnabled(false)
    abEnabled(true)
    encryptEnabled(true)
    pickerEnabled(false)
    autoTrackEnabled(true)
    h5BridgeEnabled(true)
    h5CollectEnabled(true)
    autoTrackFragmentEnabled(true)
    autoStart(true)
    clipboardEnabled(true)
    deferredALinkEnabled(true)
    h5BridgeAllowlist(listOf("*.example.com", "*.myapp.com"))
    userUniqueId("initial_user_id")
}
```

### 事件追踪 DSL

```kotlin
trackEvent("complex_event") {
    userId("user123")
    property("string_prop", "value")
    property("int_prop", 123)
    property("boolean_prop", true)
    property("double_prop", 99.99)
    property("list_prop", listOf("item1", "item2"))
    property("map_prop", mapOf("key" to "value"))
    
    // 便捷属性方法
    commodityName("商品名")
    price(999.0)
    color("红色")
    searchKeywords("搜索词")
    payMethod("支付宝")
}
```

## 🔄 与 Java 版本的对比

### Java 版本
```java
// 初始化
AppLogConfig config = new AppLogConfig.Builder("app_id")
    .channel("test")
    .logEnabled(true)
    .build();
AppLogManager.getInstance().initialize(this, config);

// 事件上报
Map<String, Object> properties = new HashMap<>();
properties.put("product_name", "iPhone");
properties.put("price", 8999);
AppLogUtils.trackEvent("purchase", properties, "user123");
```

### Kotlin 版本
```kotlin
// 初始化
initAppLog("app_id") {
    channel("test")
    logEnabled(true)
}

// 事件上报
trackEvent("purchase") {
    userId("user123")
    property("product_name", "iPhone")
    property("price", 8999)
}
```

## 🎯 最佳实践

### 1. 在 Application 中初始化

```kotlin
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        
        // 根据构建类型选择初始化方式
        when {
            BuildConfig.DEBUG -> initAppLogDev(APP_ID)
            else -> initAppLogProd(APP_ID)
        }
    }
}
```

### 2. Activity 基类中的生命周期追踪

```kotlin
abstract class BaseActivity : AppCompatActivity() {
    
    protected abstract val pageName: String
    protected open val userId: String? get() = getCurrentUserId()
    
    override fun onResume() {
        super.onResume()
        trackEvent("page_enter") {
            userId(userId)
            property("page_name", pageName)
            property("enter_time", System.currentTimeMillis())
        }
    }
    
    override fun onPause() {
        super.onPause()
        trackEvent("page_leave") {
            userId(userId)
            property("page_name", pageName)
            property("leave_time", System.currentTimeMillis())
        }
    }
}
```

### 3. 协程中的错误处理

```kotlin
class AppLogRepository {
    
    suspend fun initializeWithRetry(maxRetries: Int = 3) {
        repeat(maxRetries) { attempt ->
            try {
                val config = AppLogConfig.createProduction(APP_ID)
                AppLogManager.initializeAsync(application, config).getOrThrow()
                return // 成功则返回
            } catch (e: Exception) {
                if (attempt == maxRetries - 1) throw e
                delay(1000 * (attempt + 1)) // 指数退避
            }
        }
    }
}
```

### 4. 类型安全的事件定义

```kotlin
object AppEvents {
    const val PAGE_VIEW = "page_view"
    const val PURCHASE = "purchase"
    const val SEARCH = "search"
    
    // 扩展函数提供类型安全的事件追踪
    fun trackPageView(userId: String?, productName: String, price: Double) {
        trackEvent(PAGE_VIEW) {
            userId(userId)
            commodityName(productName)
            price(price)
        }
    }
}
```

## 🚨 注意事项

1. **协程作用域**: 确保在合适的作用域中使用协程
2. **空值安全**: 充分利用 Kotlin 的空值安全特性
3. **性能**: 内联函数和扩展函数不会产生额外开销
4. **兼容性**: Kotlin 版本与 Java 版本可以共存
5. **错误处理**: 使用 Result 类型处理异步操作的结果

这个 Kotlin 封装版本充分利用了 Kotlin 的语言特性，提供了更现代化、类型安全和简洁的 API，同时保持了与原始功能的完全兼容性。
