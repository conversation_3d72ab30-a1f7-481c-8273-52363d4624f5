package com.bytedance.applog.kotlin

import android.app.Application
import android.util.Log
import androidx.annotation.Nullable
import com.bytedance.applog.AppLog
import com.bytedance.applog.ILogger
import com.bytedance.applog.InitConfig
import com.bytedance.applog.alink.IALinkListener
import com.bytedance.applog.picker.Picker
import kotlinx.coroutines.*

/**
 * AppLog统一管理器 - Kotlin版本
 * 提供AppLog SDK的统一初始化和管理功能
 * 
 * <AUTHOR>
 */
object AppLogManager {
    
    private const val TAG = "AppLogManager"
    
    @Volatile
    private var isInitialized = false
    private var config: AppLogConfig? = null
    private var application: Application? = null
    
    // 协程作用域
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    /**
     * 初始化监听器接口
     */
    interface InitializationListener {
        fun onInitSuccess()
        fun onInitFailure(exception: Exception)
    }
    
    /**
     * ALink监听器接口
     */
    interface ALinkListener {
        fun onALinkData(data: Map<String, String>?, error: Exception?)
        fun onAttributionData(data: Map<String, String>?, error: Exception?)
    }
    
    /**
     * 设备ID回调接口
     */
    interface DeviceIdCallback {
        fun onDeviceIdReady(deviceId: String, ssid: String)
        fun onError(exception: Exception)
    }
    
    /**
     * 初始化AppLog SDK
     */
    fun initialize(
        application: Application,
        config: AppLogConfig,
        listener: InitializationListener? = null
    ) {
        if (isInitialized) {
            Log.w(TAG, "AppLog is already initialized")
            listener?.onInitSuccess()
            return
        }
        
        try {
            this.application = application
            this.config = config
            
            // 创建初始化配置
            val initConfig = createInitConfig(application, config)
            
            // 设置加密和压缩
            AppLog.setEncryptAndCompress(config.encryptEnabled)
            
            // 设置剪切板功能
            if (config.clipboardEnabled) {
                AppLog.setClipboardEnabled(true)
            }
            
            // 初始化SDK
            AppLog.init(application, initConfig)
            
            isInitialized = true
            Log.i(TAG, "AppLog initialized successfully")
            
            listener?.onInitSuccess()
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize AppLog", e)
            listener?.onInitFailure(e)
        }
    }
    
    /**
     * 协程版本的初始化
     */
    suspend fun initializeAsync(
        application: Application,
        config: AppLogConfig
    ): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val deferred = CompletableDeferred<Unit>()
            
            initialize(application, config, object : InitializationListener {
                override fun onInitSuccess() {
                    deferred.complete(Unit)
                }
                
                override fun onInitFailure(exception: Exception) {
                    deferred.completeExceptionally(exception)
                }
            })
            
            deferred.await()
            Result.success(Unit)
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 创建初始化配置
     */
    private fun createInitConfig(application: Application, config: AppLogConfig): InitConfig {
        return InitConfig(config.appId, config.channel).apply {
            // 设置URI配置
            uriConfig = config.createUriConfig()
            
            // 设置AB测试
            setAbEnable(config.abEnabled)
            
            // 设置日志
            setLogEnable(config.logEnabled)
            if (config.logEnabled) {
                setLogger(object : ILogger {
                    override fun log(s: String, throwable: Throwable?) {
                        Log.d("AppLog", s)
                    }
                })
            }
            
            // 设置圈选埋点
            if (config.pickerEnabled) {
                setPicker(Picker(application, this))
            }
            
            // 设置自动埋点
            setAutoTrackEnabled(config.autoTrackEnabled)
            setAutoTrackFragmentEnabled(config.autoTrackFragmentEnabled)
            
            // 设置H5相关配置
            setH5BridgeEnable(config.h5BridgeEnabled)
            setH5CollectEnable(config.h5CollectEnabled)
            setH5BridgeAllowlist(config.h5BridgeAllowlist)
            
            // 设置自动启动
            setAutoStart(config.autoStart)
            
            // 设置用户ID
            config.userUniqueId?.let { setUserUniqueId(it) }
            
            // 启用延迟深度链接
            if (config.deferredALinkEnabled) {
                enableDeferredALink()
            }
        }
    }
    
    /**
     * 设置ALink监听器
     */
    fun setALinkListener(listener: ALinkListener) {
        AppLog.setALinkListener(object : IALinkListener {
            override fun onALinkData(@Nullable map: Map<String, String>?, @Nullable e: Exception?) {
                listener.onALinkData(map, e)
            }
            
            override fun onAttributionData(@Nullable map: Map<String, String>?, @Nullable e: Exception?) {
                listener.onAttributionData(map, e)
            }
        })
    }
    
    /**
     * 获取设备ID
     */
    fun getDeviceId(): String? {
        if (!isInitialized) {
            Log.w(TAG, "AppLog not initialized, device ID may not be available")
        }
        return AppLog.getDid()
    }
    
    /**
     * 获取SSID
     */
    fun getSSID(): String? {
        if (!isInitialized) {
            Log.w(TAG, "AppLog not initialized, SSID may not be available")
        }
        return AppLog.getSsid()
    }
    
    /**
     * 异步获取设备ID（协程版本）
     */
    suspend fun getDeviceIdAsync(): Pair<String?, String?> = withContext(Dispatchers.IO) {
        delay(1200) // 等待设备注册完成
        Pair(getDeviceId(), getSSID())
    }
    
    /**
     * 等待设备注册完成并获取设备ID（回调版本）
     */
    fun getDeviceIdAsync(callback: DeviceIdCallback) {
        scope.launch {
            try {
                val (deviceId, ssid) = getDeviceIdAsync()
                callback.onDeviceIdReady(deviceId ?: "", ssid ?: "")
            } catch (e: Exception) {
                callback.onError(e)
            }
        }
    }
    
    /**
     * 检查是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized
    
    /**
     * 获取当前配置
     */
    fun getConfig(): AppLogConfig? = config
    
    /**
     * 获取事件管理器
     */
    fun getEventManager(): AppLogEventManager = AppLogEventManager
    
    /**
     * 获取AB测试管理器
     */
    fun getABTestManager(): AppLogABTestManager = AppLogABTestManager
    
    /**
     * 快速初始化方法（使用默认配置）
     */
    fun quickInit(application: Application, appId: String) {
        val config = AppLogConfig.createDefault(appId)
        initialize(application, config)
    }
    
    /**
     * 快速初始化方法（开发环境）
     */
    fun quickInitDev(application: Application, appId: String) {
        val config = AppLogConfig.createDevelopment(appId)
        initialize(application, config)
    }
    
    /**
     * 快速初始化方法（生产环境）
     */
    fun quickInitProd(application: Application, appId: String) {
        val config = AppLogConfig.createProduction(appId)
        initialize(application, config)
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
    }
}
