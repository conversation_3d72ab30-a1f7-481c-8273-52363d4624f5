package com.bytedance.applog.kotlin

import android.util.Log
import com.bytedance.applog.AppLog

/**
 * AppLog AB测试管理器 - Kotlin版本
 * 统一管理AB测试相关功能
 * 
 * <AUTHOR>
 */
object AppLogABTestManager {
    
    private const val TAG = "AppLogABTestManager"
    
    // AB测试结果缓存
    private val abConfigCache = mutableMapOf<String, String>()
    
    /**
     * AB测试监听器接口
     */
    interface ABTestListener {
        fun onABTestResult(experimentKey: String, experimentValue: String)
        fun onABTestError(experimentKey: String, error: String)
    }
    
    /**
     * 获取AB测试配置
     */
    fun getABConfig(experimentKey: String, defaultValue: String): String {
        return try {
            val result = AppLog.getAbConfig(experimentKey, defaultValue)
            
            // 缓存结果
            abConfigCache[experimentKey] = result
            
            Log.d(TAG, "AB Test - Key: $experimentKey, Value: $result")
            result
            
        } catch (e: Exception) {
            Log.e(TAG, "Error getting AB config for key: $experimentKey", e)
            defaultValue
        }
    }
    
    /**
     * 获取AB测试配置（带监听器）
     */
    fun getABConfig(
        experimentKey: String,
        defaultValue: String,
        listener: ABTestListener?
    ): String {
        return try {
            val result = getABConfig(experimentKey, defaultValue)
            listener?.onABTestResult(experimentKey, result)
            result
            
        } catch (e: Exception) {
            Log.e(TAG, "Error getting AB config for key: $experimentKey", e)
            listener?.onABTestError(experimentKey, e.message ?: "Unknown error")
            defaultValue
        }
    }
    
    /**
     * 获取布尔类型的AB测试配置
     */
    fun getABConfigBoolean(experimentKey: String, defaultValue: Boolean): Boolean {
        val result = getABConfig(experimentKey, defaultValue.toString())
        return result.toBoolean()
    }
    
    /**
     * 获取整数类型的AB测试配置
     */
    fun getABConfigInt(experimentKey: String, defaultValue: Int): Int {
        val result = getABConfig(experimentKey, defaultValue.toString())
        return try {
            result.toInt()
        } catch (e: NumberFormatException) {
            Log.w(TAG, "Failed to parse AB config as int: $result, using default: $defaultValue")
            defaultValue
        }
    }
    
    /**
     * 获取浮点数类型的AB测试配置
     */
    fun getABConfigDouble(experimentKey: String, defaultValue: Double): Double {
        val result = getABConfig(experimentKey, defaultValue.toString())
        return try {
            result.toDouble()
        } catch (e: NumberFormatException) {
            Log.w(TAG, "Failed to parse AB config as double: $result, using default: $defaultValue")
            defaultValue
        }
    }
    
    /**
     * 检查是否在实验组
     */
    fun isInExperimentGroup(experimentKey: String, experimentValue: String): Boolean {
        val currentValue = getABConfig(experimentKey, "")
        return experimentValue == currentValue
    }
    
    /**
     * 获取缓存的AB测试结果
     */
    fun getCachedABConfig(experimentKey: String): String? {
        return abConfigCache[experimentKey]
    }
    
    /**
     * 清除AB测试缓存
     */
    fun clearABConfigCache() {
        abConfigCache.clear()
        Log.d(TAG, "AB config cache cleared")
    }
    
    /**
     * 获取所有缓存的AB测试配置
     */
    fun getAllCachedABConfigs(): Map<String, String> {
        return abConfigCache.toMap()
    }
    
    /**
     * 预加载AB测试配置
     */
    fun preloadABConfigs(experiments: Map<String, String>) {
        experiments.forEach { (key, defaultValue) ->
            getABConfig(key, defaultValue)
        }
        Log.d(TAG, "Preloaded ${experiments.size} AB configs")
    }
    
    /**
     * 预加载AB测试配置（数组版本）
     */
    fun preloadABConfigs(experimentKeys: Array<String>, defaultValues: Array<String>) {
        if (experimentKeys.size != defaultValues.size) {
            Log.w(TAG, "Invalid parameters for preloading AB configs")
            return
        }
        
        experimentKeys.forEachIndexed { index, key ->
            getABConfig(key, defaultValues[index])
        }
        
        Log.d(TAG, "Preloaded ${experimentKeys.size} AB configs")
    }
    
    /**
     * 常用的AB测试场景
     */
    object CommonExperiments {
        const val BUTTON_COLOR = "button_color"
        const val FEATURE_ENABLED = "feature_enabled"
        const val PRICE_STRATEGY = "price_strategy"
        const val UI_VERSION = "ui_version"
        
        /**
         * 检查功能是否启用
         */
        fun isFeatureEnabled(featureKey: String): Boolean {
            return getABConfigBoolean(featureKey, false)
        }
        
        /**
         * 获取UI版本
         */
        fun getUIVersion(): String {
            return getABConfig(UI_VERSION, "default")
        }
        
        /**
         * 获取按钮颜色
         */
        fun getButtonColor(): String {
            return getABConfig(BUTTON_COLOR, "blue")
        }
    }
}

/**
 * AB配置构建器 - 支持DSL风格
 */
class ABConfigBuilder(private val experimentKey: String) {
    private var defaultValue: String = ""
    private var listener: AppLogABTestManager.ABTestListener? = null
    
    fun defaultValue(value: String) = apply { this.defaultValue = value }
    fun defaultValue(value: Boolean) = apply { this.defaultValue = value.toString() }
    fun defaultValue(value: Int) = apply { this.defaultValue = value.toString() }
    fun defaultValue(value: Double) = apply { this.defaultValue = value.toString() }
    
    fun listener(listener: AppLogABTestManager.ABTestListener) = apply { this.listener = listener }
    
    fun get(): String {
        return AppLogABTestManager.getABConfig(experimentKey, defaultValue, listener)
    }
    
    fun getBoolean(): Boolean {
        val result = get()
        return result.toBoolean()
    }
    
    fun getInt(): Int {
        return try {
            get().toInt()
        } catch (e: NumberFormatException) {
            AppLogABTestManager.getABConfigInt(experimentKey, 0)
        }
    }
    
    fun getDouble(): Double {
        return try {
            get().toDouble()
        } catch (e: NumberFormatException) {
            AppLogABTestManager.getABConfigDouble(experimentKey, 0.0)
        }
    }
}

/**
 * 创建AB配置构建器的扩展函数
 */
fun abConfig(experimentKey: String): ABConfigBuilder {
    return ABConfigBuilder(experimentKey)
}

/**
 * 简化的AB测试DSL
 */
inline fun <T> abTest(
    experimentKey: String,
    defaultValue: T,
    crossinline onResult: (T) -> Unit
) {
    val result = when (defaultValue) {
        is String -> AppLogABTestManager.getABConfig(experimentKey, defaultValue) as T
        is Boolean -> AppLogABTestManager.getABConfigBoolean(experimentKey, defaultValue) as T
        is Int -> AppLogABTestManager.getABConfigInt(experimentKey, defaultValue) as T
        is Double -> AppLogABTestManager.getABConfigDouble(experimentKey, defaultValue) as T
        else -> defaultValue
    }
    onResult(result)
}
