package com.bytedance.applog.kotlin

import android.app.Application

/**
 * AppLog工具类 - Kotlin版本
 * 提供便捷的静态方法和扩展函数，简化AppLog的使用
 * 
 * <AUTHOR>
 */
object AppLogUtils {
    
    /**
     * 快速初始化AppLog
     */
    fun init(application: Application, appId: String) {
        AppLogManager.quickInit(application, appId)
    }
    
    /**
     * 快速初始化AppLog（开发环境）
     */
    fun initDev(application: Application, appId: String) {
        AppLogManager.quickInitDev(application, appId)
    }
    
    /**
     * 快速初始化AppLog（生产环境）
     */
    fun initProd(application: Application, appId: String) {
        AppLogManager.quickInitProd(application, appId)
    }
    
    /**
     * 设置用户ID
     */
    fun setUserId(userId: String?) {
        AppLogManager.getEventManager().setUserId(userId)
    }
    
    /**
     * 上报事件
     */
    fun trackEvent(eventName: String, properties: Map<String, Any>? = null) {
        AppLogManager.getEventManager().trackEvent(eventName, properties)
    }
    
    /**
     * 上报事件（带用户ID）
     */
    fun trackEvent(eventName: String, properties: Map<String, Any>? = null, userId: String?) {
        AppLogManager.getEventManager().trackEvent(eventName, properties, userId)
    }
    
    /**
     * 浏览商品
     */
    fun trackPageView(userId: String?, commodityName: String, price: Double, color: String) {
        AppLogManager.getEventManager().trackPageView(userId, commodityName, price, color)
    }
    
    /**
     * 搜索
     */
    fun trackSearch(userId: String?, keywords: String, resultDetails: String) {
        AppLogManager.getEventManager().trackSearch(userId, keywords, resultDetails)
    }
    
    /**
     * 添加购物车
     */
    fun trackAddCart(userId: String?, commodityName: String, price: Double, color: String) {
        AppLogManager.getEventManager().trackAddCart(userId, commodityName, price, color)
    }
    
    /**
     * 支付
     */
    fun trackPay(userId: String?, commodityName: String, price: Double, payMethod: String) {
        AppLogManager.getEventManager().trackPay(userId, commodityName, price, payMethod)
    }
    
    /**
     * 登录
     */
    fun trackLogin(userId: String?, loginMethod: String) {
        AppLogManager.getEventManager().trackLogin(userId, loginMethod)
    }
    
    /**
     * 登出
     */
    fun trackLogout(userId: String?) {
        AppLogManager.getEventManager().trackLogout(userId)
    }
    
    /**
     * 注册
     */
    fun trackRegister(userId: String?, registerMethod: String) {
        AppLogManager.getEventManager().trackRegister(userId, registerMethod)
    }
    
    /**
     * 设置用户属性
     */
    fun setUserProfile(userId: String?, userProperties: Map<String, Any>) {
        AppLogManager.getEventManager().setUserProfile(userId, userProperties)
    }
    
    /**
     * 设置用户属性（便捷方法）
     */
    fun setUserProfile(userId: String?, userName: String, gender: String, age: Int, skill: String) {
        AppLogManager.getEventManager().setUserProfile(userId, userName, gender, age, skill)
    }
    
    /**
     * 设置公共属性
     */
    fun setCommonProperties(commonProperties: Map<String, Any>) {
        AppLogManager.getEventManager().setCommonProperties(commonProperties)
    }
    
    /**
     * 获取AB测试配置
     */
    fun getABConfig(experimentKey: String, defaultValue: String): String {
        return AppLogManager.getABTestManager().getABConfig(experimentKey, defaultValue)
    }
    
    /**
     * 获取AB测试配置（布尔类型）
     */
    fun getABConfigBoolean(experimentKey: String, defaultValue: Boolean): Boolean {
        return AppLogManager.getABTestManager().getABConfigBoolean(experimentKey, defaultValue)
    }
    
    /**
     * 获取AB测试配置（整数类型）
     */
    fun getABConfigInt(experimentKey: String, defaultValue: Int): Int {
        return AppLogManager.getABTestManager().getABConfigInt(experimentKey, defaultValue)
    }
    
    /**
     * 检查是否在实验组
     */
    fun isInExperimentGroup(experimentKey: String, experimentValue: String): Boolean {
        return AppLogManager.getABTestManager().isInExperimentGroup(experimentKey, experimentValue)
    }
    
    /**
     * 获取设备ID
     */
    fun getDeviceId(): String? {
        return AppLogManager.getDeviceId()
    }
    
    /**
     * 获取SSID
     */
    fun getSSID(): String? {
        return AppLogManager.getSSID()
    }
}

/**
 * DSL风格的事件构建函数
 */
fun event(eventName: String): EventBuilder {
    return EventBuilder(eventName)
}

/**
 * DSL风格的用户属性构建函数
 */
fun userProfile(userId: String?): UserProfileBuilder {
    return UserProfileBuilder(userId)
}

/**
 * Application扩展函数 - 初始化AppLog
 */
fun Application.initAppLog(appId: String, block: (AppLogConfig.Builder.() -> Unit)? = null) {
    if (block != null) {
        val config = appLogConfig(appId, block)
        AppLogManager.initialize(this, config)
    } else {
        AppLogUtils.init(this, appId)
    }
}

/**
 * Application扩展函数 - 开发环境初始化
 */
fun Application.initAppLogDev(appId: String) {
    AppLogUtils.initDev(this, appId)
}

/**
 * Application扩展函数 - 生产环境初始化
 */
fun Application.initAppLogProd(appId: String) {
    AppLogUtils.initProd(this, appId)
}

/**
 * String扩展函数 - 设置用户ID
 */
fun String.setAsUserId() {
    AppLogUtils.setUserId(this)
}

/**
 * Map扩展函数 - 设置为公共属性
 */
fun Map<String, Any>.setAsCommonProperties() {
    AppLogUtils.setCommonProperties(this)
}

/**
 * 内联函数 - 事件追踪DSL
 */
inline fun trackEvent(eventName: String, block: EventBuilder.() -> Unit) {
    event(eventName).apply(block).track()
}

/**
 * 内联函数 - 用户属性设置DSL
 */
inline fun setUserProfile(userId: String?, block: UserProfileBuilder.() -> Unit) {
    userProfile(userId).apply(block).set()
}

/**
 * 内联函数 - AB测试DSL
 */
inline fun <T> withABTest(
    experimentKey: String,
    defaultValue: T,
    crossinline action: (T) -> Unit
) {
    abTest(experimentKey, defaultValue, action)
}

/**
 * 便捷的事件追踪扩展函数
 */
fun String.trackAsPageView(userId: String?, price: Double, color: String) {
    AppLogUtils.trackPageView(userId, this, price, color)
}

fun String.trackAsSearch(userId: String?, resultDetails: String) {
    AppLogUtils.trackSearch(userId, this, resultDetails)
}

/**
 * 便捷的AB测试扩展函数
 */
fun String.asABConfig(defaultValue: String): String {
    return AppLogUtils.getABConfig(this, defaultValue)
}

fun String.asABConfigBoolean(defaultValue: Boolean): Boolean {
    return AppLogUtils.getABConfigBoolean(this, defaultValue)
}

fun String.asABConfigInt(defaultValue: Int): Int {
    return AppLogUtils.getABConfigInt(this, defaultValue)
}

/**
 * 协程扩展 - 异步获取设备信息
 */
suspend fun getDeviceInfoAsync(): Pair<String?, String?> {
    return AppLogManager.getDeviceIdAsync()
}

/**
 * 高阶函数 - 安全执行AppLog操作
 */
inline fun safeAppLogOperation(operation: () -> Unit) {
    try {
        if (AppLogManager.isInitialized()) {
            operation()
        }
    } catch (e: Exception) {
        // 静默处理错误，不影响业务逻辑
    }
}

/**
 * 批量事件追踪
 */
fun trackEvents(vararg events: Pair<String, Map<String, Any>?>) {
    events.forEach { (eventName, properties) ->
        safeAppLogOperation {
            AppLogUtils.trackEvent(eventName, properties)
        }
    }
}

/**
 * 条件事件追踪
 */
inline fun trackEventIf(condition: Boolean, eventName: String, block: EventBuilder.() -> Unit) {
    if (condition) {
        trackEvent(eventName, block)
    }
}
