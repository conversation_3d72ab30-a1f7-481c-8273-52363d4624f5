package com.bytedance.applog.kotlin

import android.app.Application
import android.util.Log
import com.bytedance.util.Constant
import kotlinx.coroutines.*

/**
 * AppLog封装库测试类 - Kotlin版本
 * 用于验证Kotlin封装功能是否正常工作
 * 
 * <AUTHOR>
 */
object AppLogTest {
    
    private const val TAG = "AppLogTest"
    
    /**
     * 测试基础功能
     */
    suspend fun testBasicFunctions(application: Application) {
        Log.i(TAG, "开始测试AppLog Kotlin封装库基础功能")
        
        try {
            // 1. 测试初始化
            testInitialization(application)
            
            // 2. 测试事件上报
            testEventTracking()
            
            // 3. 测试用户属性
            testUserProfile()
            
            // 4. 测试AB测试
            testABTesting()
            
            // 5. 测试设备信息
            testDeviceInfo()
            
            Log.i(TAG, "AppLog Kotlin封装库基础功能测试完成")
        } catch (e: Exception) {
            Log.e(TAG, "测试过程中发生错误", e)
        }
    }
    
    /**
     * 测试初始化功能
     */
    private suspend fun testInitialization(application: Application) {
        Log.i(TAG, "测试初始化功能")
        
        try {
            // 测试快速初始化
            application.initAppLogDev(Constant.appId)
            Log.i(TAG, "✓ 快速初始化成功")
            
            // 测试DSL配置初始化
            val config = appLogConfig(Constant.appId) {
                channel("test-channel")
                logEnabled(true)
                abEnabled(true)
                encryptEnabled(false)
            }
            Log.i(TAG, "✓ DSL配置创建成功: $config")
            
            // 测试协程初始化
            val result = AppLogManager.initializeAsync(application, config)
            result.onSuccess {
                Log.i(TAG, "✓ 协程初始化成功")
            }.onFailure { exception ->
                Log.w(TAG, "协程初始化失败", exception)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "✗ 初始化测试失败", e)
        }
    }
    
    /**
     * 测试事件上报功能
     */
    private fun testEventTracking() {
        Log.i(TAG, "测试事件上报功能")
        
        try {
            val testUserId = "test_user_123"
            
            // 测试便捷方法
            AppLogUtils.trackPageView(testUserId, "测试商品", 999.99, "红色")
            AppLogUtils.trackSearch(testUserId, "测试搜索", "/test/search")
            AppLogUtils.trackAddCart(testUserId, "测试商品", 999.99, "红色")
            Log.i(TAG, "✓ 便捷方法事件上报成功")
            
            // 测试DSL风格
            trackEvent("test_custom_event") {
                userId(testUserId)
                property("test_prop1", "value1")
                property("test_prop2", 123)
                commodityName("测试商品")
                price(999.99)
            }
            Log.i(TAG, "✓ DSL风格事件上报成功")
            
            // 测试扩展函数
            "测试商品".trackAsPageView(testUserId, 888.88, "蓝色")
            "测试关键词".trackAsSearch(testUserId, "/test/search/result")
            Log.i(TAG, "✓ 扩展函数事件上报成功")
            
            // 测试条件事件追踪
            trackEventIf(testUserId.isNotEmpty(), "conditional_event") {
                userId(testUserId)
                property("condition", "user_exists")
            }
            Log.i(TAG, "✓ 条件事件追踪成功")
            
            // 测试批量事件追踪
            trackEvents(
                "batch_event_1" to mapOf("prop1" to "value1"),
                "batch_event_2" to mapOf("prop2" to "value2"),
                "batch_event_3" to null
            )
            Log.i(TAG, "✓ 批量事件追踪成功")
            
        } catch (e: Exception) {
            Log.e(TAG, "✗ 事件上报测试失败", e)
        }
    }
    
    /**
     * 测试用户属性功能
     */
    private fun testUserProfile() {
        Log.i(TAG, "测试用户属性功能")
        
        try {
            val testUserId = "test_user_123"
            
            // 测试便捷方法
            AppLogUtils.setUserProfile(testUserId, "测试用户", "男", 25, "测试技能")
            Log.i(TAG, "✓ 便捷方法用户属性设置成功")
            
            // 测试DSL风格
            setUserProfile(testUserId) {
                userName("测试用户2")
                gender("女")
                age(30)
                skill("Kotlin开发")
                property("city", "北京")
                property("education", "本科")
            }
            Log.i(TAG, "✓ DSL风格用户属性设置成功")
            
            // 测试扩展函数设置用户ID
            testUserId.setAsUserId()
            Log.i(TAG, "✓ 扩展函数设置用户ID成功")
            
        } catch (e: Exception) {
            Log.e(TAG, "✗ 用户属性测试失败", e)
        }
    }
    
    /**
     * 测试AB测试功能
     */
    private fun testABTesting() {
        Log.i(TAG, "测试AB测试功能")
        
        try {
            // 测试基础AB配置获取
            val abValue = AppLogUtils.getABConfig("test_ab", "default")
            val abBoolean = AppLogUtils.getABConfigBoolean("test_feature", false)
            val abInt = AppLogUtils.getABConfigInt("test_number", 10)
            Log.i(TAG, "✓ AB配置获取成功: $abValue, $abBoolean, $abInt")
            
            // 测试扩展函数
            val themeValue = "ui_theme".asABConfig("light")
            val featureEnabled = "new_feature".asABConfigBoolean(false)
            val retryCount = "retry_count".asABConfigInt(3)
            Log.i(TAG, "✓ 扩展函数AB配置获取成功: $themeValue, $featureEnabled, $retryCount")
            
            // 测试DSL风格
            abConfig("test_ab_builder") {
                defaultValue("builder_default")
                listener(object : AppLogABTestManager.ABTestListener {
                    override fun onABTestResult(experimentKey: String, experimentValue: String) {
                        Log.i(TAG, "✓ AB测试监听器回调成功: $experimentKey = $experimentValue")
                    }
                    
                    override fun onABTestError(experimentKey: String, error: String) {
                        Log.w(TAG, "AB测试错误: $experimentKey - $error")
                    }
                })
            }.get()
            Log.i(TAG, "✓ DSL风格AB测试成功")
            
            // 测试高阶函数
            withABTest("feature_test", false) { enabled ->
                Log.i(TAG, "✓ 高阶函数AB测试成功: $enabled")
            }
            
            // 测试常用实验
            val isNewUIEnabled = AppLogABTestManager.CommonExperiments.isFeatureEnabled("new_ui")
            val uiVersion = AppLogABTestManager.CommonExperiments.getUIVersion()
            Log.i(TAG, "✓ 常用实验测试成功: newUI=$isNewUIEnabled, version=$uiVersion")
            
        } catch (e: Exception) {
            Log.e(TAG, "✗ AB测试功能测试失败", e)
        }
    }
    
    /**
     * 测试设备信息功能
     */
    private suspend fun testDeviceInfo() {
        Log.i(TAG, "测试设备信息功能")
        
        try {
            // 测试协程版本
            val (deviceId, ssid) = getDeviceInfoAsync()
            Log.i(TAG, "✓ 协程获取设备信息成功: DID=$deviceId, SSID=$ssid")
            
            // 测试回调版本
            AppLogManager.getDeviceIdAsync(object : AppLogManager.DeviceIdCallback {
                override fun onDeviceIdReady(deviceId: String, ssid: String) {
                    Log.i(TAG, "✓ 回调获取设备信息成功: DID=$deviceId, SSID=$ssid")
                }
                
                override fun onError(exception: Exception) {
                    Log.w(TAG, "设备信息获取失败", exception)
                }
            })
            
            // 测试同步获取
            val syncDeviceId = AppLogUtils.getDeviceId()
            val syncSSID = AppLogUtils.getSSID()
            Log.i(TAG, "同步获取设备信息: DID=$syncDeviceId, SSID=$syncSSID")
            
        } catch (e: Exception) {
            Log.e(TAG, "✗ 设备信息测试失败", e)
        }
    }
    
    /**
     * 测试配置功能
     */
    fun testConfiguration() {
        Log.i(TAG, "测试配置功能")
        
        try {
            // 测试默认配置
            val defaultConfig = AppLogConfig.createDefault("test_app_id")
            Log.i(TAG, "✓ 默认配置创建成功: $defaultConfig")
            
            // 测试开发环境配置
            val devConfig = AppLogConfig.createDevelopment("test_app_id")
            Log.i(TAG, "✓ 开发环境配置创建成功: $devConfig")
            
            // 测试生产环境配置
            val prodConfig = AppLogConfig.createProduction("test_app_id")
            Log.i(TAG, "✓ 生产环境配置创建成功: $prodConfig")
            
            // 测试DSL配置
            val customConfig = appLogConfig("test_app_id") {
                channel("custom_channel")
                logEnabled(true)
                abEnabled(true)
                encryptEnabled(false)
                autoStart(true)
            }
            Log.i(TAG, "✓ DSL自定义配置创建成功: $customConfig")
            
            // 验证配置属性
            require(customConfig.appId == "test_app_id")
            require(customConfig.channel == "custom_channel")
            require(customConfig.logEnabled)
            require(customConfig.abEnabled)
            require(!customConfig.encryptEnabled)
            require(customConfig.autoStart)
            
            Log.i(TAG, "✓ 配置属性验证成功")
            
        } catch (e: Exception) {
            Log.e(TAG, "✗ 配置功能测试失败", e)
        }
    }
    
    /**
     * 测试扩展函数和DSL
     */
    fun testExtensionsAndDSL() {
        Log.i(TAG, "测试扩展函数和DSL")
        
        try {
            // 测试公共属性扩展函数
            mapOf(
                "test_prop1" to "value1",
                "test_prop2" to 123,
                "test_prop3" to true
            ).setAsCommonProperties()
            Log.i(TAG, "✓ 公共属性扩展函数测试成功")
            
            // 测试安全操作
            safeAppLogOperation {
                AppLogUtils.trackEvent("safe_test_event")
            }
            Log.i(TAG, "✓ 安全操作测试成功")
            
            // 测试事件构建器
            val eventBuilder = event("builder_test")
                .userId("test_user")
                .property("test", "value")
            Log.i(TAG, "✓ 事件构建器测试成功")
            
            // 测试用户属性构建器
            val profileBuilder = userProfile("test_user")
                .userName("测试用户")
                .age(25)
            Log.i(TAG, "✓ 用户属性构建器测试成功")
            
        } catch (e: Exception) {
            Log.e(TAG, "✗ 扩展函数和DSL测试失败", e)
        }
    }
    
    /**
     * 测试协程功能
     */
    suspend fun testCoroutineFunctions(application: Application) {
        Log.i(TAG, "测试协程功能")
        
        try {
            // 测试协程初始化
            val config = AppLogConfig.createDevelopment("test_app_id")
            val initResult = AppLogManager.initializeAsync(application, config)
            
            initResult.onSuccess {
                Log.i(TAG, "✓ 协程初始化测试成功")
            }.onFailure { exception ->
                Log.w(TAG, "协程初始化失败", exception)
            }
            
            // 测试协程获取设备信息
            val (deviceId, ssid) = withContext(Dispatchers.IO) {
                getDeviceInfoAsync()
            }
            Log.i(TAG, "✓ 协程设备信息获取测试成功: $deviceId, $ssid")
            
        } catch (e: Exception) {
            Log.e(TAG, "✗ 协程功能测试失败", e)
        }
    }
    
    /**
     * 运行所有测试
     */
    suspend fun runAllTests(application: Application) {
        Log.i(TAG, "=== 开始运行AppLog Kotlin封装库完整测试 ===")
        
        try {
            testConfiguration()
            testExtensionsAndDSL()
            testCoroutineFunctions(application)
            testBasicFunctions(application)
            
            Log.i(TAG, "=== AppLog Kotlin封装库完整测试结束 ===")
        } catch (e: Exception) {
            Log.e(TAG, "=== 测试过程中发生严重错误 ===", e)
        }
    }
}
