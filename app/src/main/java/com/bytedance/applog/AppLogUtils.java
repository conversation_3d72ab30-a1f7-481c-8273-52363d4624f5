package com.bytedance.applog;

import android.app.Application;

import java.util.HashMap;
import java.util.Map;

/**
 * AppLog工具类
 * 提供便捷的静态方法，简化AppLog的使用
 * 
 * <AUTHOR>
 */
public class AppLogUtils {
    
    /**
     * 快速初始化AppLog
     */
    public static void init(Application application, String appId) {
        AppLogManager.quickInit(application, appId);
    }
    
    /**
     * 快速初始化AppLog（开发环境）
     */
    public static void initDev(Application application, String appId) {
        AppLogManager.quickInitDev(application, appId);
    }
    
    /**
     * 快速初始化AppLog（生产环境）
     */
    public static void initProd(Application application, String appId) {
        AppLogManager.quickInitProd(application, appId);
    }
    
    /**
     * 设置用户ID
     */
    public static void setUserId(String userId) {
        AppLogManager.getInstance().getEventManager().setUserId(userId);
    }
    
    /**
     * 上报事件
     */
    public static void trackEvent(String eventName, Map<String, Object> properties) {
        AppLogManager.getInstance().getEventManager().trackEvent(eventName, properties);
    }
    
    /**
     * 上报事件（带用户ID）
     */
    public static void trackEvent(String eventName, Map<String, Object> properties, String userId) {
        AppLogManager.getInstance().getEventManager().trackEvent(eventName, properties, userId);
    }
    
    /**
     * 浏览商品
     */
    public static void trackPageView(String userId, String commodityName, double price, String color) {
        AppLogManager.getInstance().getEventManager().trackPageView(userId, commodityName, price, color);
    }
    
    /**
     * 搜索
     */
    public static void trackSearch(String userId, String keywords, String resultDetails) {
        AppLogManager.getInstance().getEventManager().trackSearch(userId, keywords, resultDetails);
    }
    
    /**
     * 添加购物车
     */
    public static void trackAddCart(String userId, String commodityName, double price, String color) {
        AppLogManager.getInstance().getEventManager().trackAddCart(userId, commodityName, price, color);
    }
    
    /**
     * 支付
     */
    public static void trackPay(String userId, String commodityName, double price, String payMethod) {
        AppLogManager.getInstance().getEventManager().trackPay(userId, commodityName, price, payMethod);
    }
    
    /**
     * 登录
     */
    public static void trackLogin(String userId, String loginMethod) {
        AppLogManager.getInstance().getEventManager().trackLogin(userId, loginMethod);
    }
    
    /**
     * 登出
     */
    public static void trackLogout(String userId) {
        AppLogManager.getInstance().getEventManager().trackLogout(userId);
    }
    
    /**
     * 注册
     */
    public static void trackRegister(String userId, String registerMethod) {
        AppLogManager.getInstance().getEventManager().trackRegister(userId, registerMethod);
    }
    
    /**
     * 设置用户属性
     */
    public static void setUserProfile(String userId, Map<String, Object> userProperties) {
        AppLogManager.getInstance().getEventManager().setUserProfile(userId, userProperties);
    }
    
    /**
     * 设置用户属性（便捷方法）
     */
    public static void setUserProfile(String userId, String userName, String gender, int age, String skill) {
        AppLogManager.getInstance().getEventManager().setUserProfile(userId, userName, gender, age, skill);
    }
    
    /**
     * 设置公共属性
     */
    public static void setCommonProperties(Map<String, Object> commonProperties) {
        AppLogManager.getInstance().getEventManager().setCommonProperties(commonProperties);
    }
    
    /**
     * 获取AB测试配置
     */
    public static String getABConfig(String experimentKey, String defaultValue) {
        return AppLogManager.getInstance().getABTestManager().getABConfig(experimentKey, defaultValue);
    }
    
    /**
     * 获取AB测试配置（布尔类型）
     */
    public static boolean getABConfigBoolean(String experimentKey, boolean defaultValue) {
        return AppLogManager.getInstance().getABTestManager().getABConfigBoolean(experimentKey, defaultValue);
    }
    
    /**
     * 获取AB测试配置（整数类型）
     */
    public static int getABConfigInt(String experimentKey, int defaultValue) {
        return AppLogManager.getInstance().getABTestManager().getABConfigInt(experimentKey, defaultValue);
    }
    
    /**
     * 检查是否在实验组
     */
    public static boolean isInExperimentGroup(String experimentKey, String experimentValue) {
        return AppLogManager.getInstance().getABTestManager().isInExperimentGroup(experimentKey, experimentValue);
    }
    
    /**
     * 获取设备ID
     */
    public static String getDeviceId() {
        return AppLogManager.getInstance().getDeviceId();
    }
    
    /**
     * 获取SSID
     */
    public static String getSSID() {
        return AppLogManager.getInstance().getSSID();
    }
    
    /**
     * 构建器模式创建事件属性
     */
    public static class EventBuilder {
        private Map<String, Object> properties = new HashMap<>();
        private String eventName;
        private String userId;
        
        public EventBuilder(String eventName) {
            this.eventName = eventName;
        }
        
        public EventBuilder userId(String userId) {
            this.userId = userId;
            return this;
        }
        
        public EventBuilder property(String key, Object value) {
            properties.put(key, value);
            return this;
        }
        
        public EventBuilder commodityName(String commodityName) {
            return property(AppLogEventManager.PROP_COMMODITY_NAME, commodityName);
        }
        
        public EventBuilder price(double price) {
            return property(AppLogEventManager.PROP_PRICE, price);
        }
        
        public EventBuilder color(String color) {
            return property(AppLogEventManager.PROP_COLOR, color);
        }
        
        public EventBuilder searchKeywords(String keywords) {
            return property(AppLogEventManager.PROP_SEARCH_KEYWORDS, keywords);
        }
        
        public EventBuilder resultDetails(String resultDetails) {
            return property(AppLogEventManager.PROP_RESULT_DETAILS, resultDetails);
        }
        
        public EventBuilder payMethod(String payMethod) {
            return property(AppLogEventManager.PROP_PAY_METHOD, payMethod);
        }
        
        public void track() {
            trackEvent(eventName, properties, userId);
        }
    }
    
    /**
     * 创建事件构建器
     */
    public static EventBuilder event(String eventName) {
        return new EventBuilder(eventName);
    }
    
    /**
     * 构建器模式创建用户属性
     */
    public static class UserProfileBuilder {
        private Map<String, Object> properties = new HashMap<>();
        private String userId;
        
        public UserProfileBuilder(String userId) {
            this.userId = userId;
        }
        
        public UserProfileBuilder property(String key, Object value) {
            properties.put(key, value);
            return this;
        }
        
        public UserProfileBuilder userName(String userName) {
            return property(AppLogEventManager.PROP_USER_NAME, userName);
        }
        
        public UserProfileBuilder gender(String gender) {
            return property(AppLogEventManager.PROP_USER_GENDER, gender);
        }
        
        public UserProfileBuilder age(int age) {
            return property(AppLogEventManager.PROP_USER_AGE, age);
        }
        
        public UserProfileBuilder skill(String skill) {
            return property(AppLogEventManager.PROP_USER_SKILL, skill);
        }
        
        public void set() {
            setUserProfile(userId, properties);
        }
    }
    
    /**
     * 创建用户属性构建器
     */
    public static UserProfileBuilder userProfile(String userId) {
        return new UserProfileBuilder(userId);
    }
}
