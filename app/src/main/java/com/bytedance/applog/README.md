# AppLog 封装库使用指南

这是一个对火山引擎 AppLog SDK 的封装库，提供了更简洁、易用和可扩展的 API。

## 主要特性

- 🚀 **简化初始化**：提供多种初始化方式，支持自定义配置
- 📊 **统一事件管理**：封装常用事件，减少重复代码
- 🧪 **AB测试支持**：简化AB测试配置获取和管理
- 🔧 **配置管理**：统一管理所有配置项，支持不同环境
- 🛠 **工具类支持**：提供便捷的静态方法和构建器模式
- 📱 **设备信息**：简化设备ID和SSID获取
- 🔗 **ALink支持**：简化广告监测集成

## 核心类说明

### 1. AppLogManager
主要的管理类，负责SDK初始化和统一管理。

### 2. AppLogConfig
配置管理类，使用Builder模式构建配置。

### 3. AppLogEventManager
事件管理器，统一管理所有事件上报逻辑。

### 4. AppLogABTestManager
AB测试管理器，简化AB测试配置获取。

### 5. AppLogUtils
工具类，提供便捷的静态方法。

## 快速开始

### 1. 基础初始化

```java
// 在 Application 的 onCreate 中初始化
public class MyApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        
        // 最简单的初始化方式
        AppLogUtils.init(this, "your_app_id");
        
        // 或者根据环境选择
        if (BuildConfig.DEBUG) {
            AppLogUtils.initDev(this, "your_app_id");
        } else {
            AppLogUtils.initProd(this, "your_app_id");
        }
    }
}
```

### 2. 自定义配置初始化

```java
AppLogConfig config = new AppLogConfig.Builder("your_app_id")
    .channel("custom-channel")
    .logEnabled(true)
    .abEnabled(true)
    .encryptEnabled(false)
    .domain("https://your-domain.com")
    .build();

AppLogManager.getInstance().initialize(this, config, new AppLogManager.InitializationListener() {
    @Override
    public void onInitSuccess() {
        Log.i("AppLog", "初始化成功");
    }
    
    @Override
    public void onInitFailure(Exception e) {
        Log.e("AppLog", "初始化失败", e);
    }
});
```

## 事件上报

### 1. 使用便捷方法

```java
String userId = "user123";

// 浏览商品
AppLogUtils.trackPageView(userId, "MacBook Pro", 20000, "white");

// 搜索
AppLogUtils.trackSearch(userId, "电脑", "/file/computers/details");

// 添加购物车
AppLogUtils.trackAddCart(userId, "MacBook Pro", 20000, "white");

// 支付
AppLogUtils.trackPay(userId, "MacBook Pro", 19500, "creditCard");

// 登录
AppLogUtils.trackLogin(userId, "email");

// 登出
AppLogUtils.trackLogout(userId);
```

### 2. 使用构建器模式

```java
// 自定义事件
AppLogUtils.event("custom_event")
    .userId("user123")
    .property("product_id", "12345")
    .property("category", "electronics")
    .property("price", 999.99)
    .track();

// 商品浏览事件
AppLogUtils.event(AppLogEventManager.EVENT_PAGE_VIEW)
    .userId("user123")
    .commodityName("iPhone 14")
    .price(8999)
    .color("black")
    .track();
```

### 3. 使用Map方式

```java
Map<String, Object> properties = new HashMap<>();
properties.put("product_id", "12345");
properties.put("category", "electronics");
AppLogUtils.trackEvent("product_view", properties, "user123");
```

## 用户属性设置

### 1. 使用便捷方法

```java
AppLogUtils.setUserProfile("user123", "张三", "男", 25, "Java开发");
```

### 2. 使用构建器模式

```java
AppLogUtils.userProfile("user123")
    .userName("张三")
    .gender("男")
    .age(25)
    .skill("Java开发")
    .property("vip_level", "gold")
    .property("registration_date", "2023-01-01")
    .set();
```

## AB测试

### 1. 基础用法

```java
// 获取字符串配置
String buttonColor = AppLogUtils.getABConfig("button_color", "blue");

// 获取布尔配置
boolean newFeatureEnabled = AppLogUtils.getABConfigBoolean("new_feature", false);

// 获取整数配置
int maxRetries = AppLogUtils.getABConfigInt("max_retries", 3);

// 检查是否在实验组
boolean inExperiment = AppLogUtils.isInExperimentGroup("ab1", "experiment_group");
```

### 2. 使用监听器

```java
AppLogABTestManager.config("ab1")
    .defaultValue("default")
    .listener(new AppLogABTestManager.ABTestListener() {
        @Override
        public void onABTestResult(String experimentKey, String experimentValue) {
            // 处理AB测试结果
            handleABTestResult(experimentKey, experimentValue);
        }
        
        @Override
        public void onABTestError(String experimentKey, String error) {
            Log.e("AB Test", "错误: " + error);
        }
    })
    .get();
```

### 3. 常用实验

```java
// 检查功能是否启用
if (AppLogABTestManager.CommonExperiments.isFeatureEnabled("new_ui")) {
    // 启用新UI
}

// 获取UI版本
String uiVersion = AppLogABTestManager.CommonExperiments.getUIVersion();

// 获取按钮颜色
String buttonColor = AppLogABTestManager.CommonExperiments.getButtonColor();
```

## 公共属性

```java
Map<String, Object> commonProps = new HashMap<>();
commonProps.put("app_version", "1.0.0");
commonProps.put("platform", "android");
commonProps.put("channel", "google_play");

AppLogUtils.setCommonProperties(commonProps);
```

## 设备信息获取

### 1. 异步获取

```java
AppLogManager.getInstance().getDeviceIdAsync(new AppLogManager.DeviceIdCallback() {
    @Override
    public void onDeviceIdReady(String deviceId, String ssid) {
        Log.i("Device", "设备ID: " + deviceId + ", SSID: " + ssid);
    }
    
    @Override
    public void onError(Exception e) {
        Log.e("Device", "获取设备ID失败", e);
    }
});
```

### 2. 同步获取

```java
String deviceId = AppLogUtils.getDeviceId();
String ssid = AppLogUtils.getSSID();
```

## ALink 广告监测

```java
AppLogManager.getInstance().setALinkListener(new AppLogManager.ALinkListener() {
    @Override
    public void onALinkData(Map<String, String> data, Exception error) {
        if (error == null && data != null) {
            // 处理ALink数据
        }
    }
    
    @Override
    public void onAttributionData(Map<String, String> data, Exception error) {
        if (error == null && data != null) {
            // 处理归因数据
        }
    }
});
```

## 最佳实践

### 1. 初始化时机
建议在 Application 的 onCreate 方法中进行初始化。

### 2. 用户ID设置
在用户登录后立即设置用户ID：
```java
AppLogUtils.setUserId(userId);
```

### 3. 错误处理
使用监听器处理初始化和AB测试的错误情况。

### 4. 性能优化
- 使用异步方式获取设备ID
- 预加载常用的AB测试配置
- 合理设置公共属性，避免重复上报

### 5. 环境区分
根据不同环境使用不同的配置：
- 开发环境：启用日志，关闭加密
- 生产环境：关闭日志，启用加密

## 迁移指南

如果你正在从原始的AppLog代码迁移到这个封装库：

### 原始代码
```java
// 初始化
InitConfig config = new InitConfig(appId, "test-new");
// ... 大量配置代码
AppLog.init(this, config);

// 事件上报
JSONObject paramsObj = new JSONObject();
paramsObj.put("commodity_name", "macBookPro");
paramsObj.put("price", 20000);
AppLog.onEventV3("pageView", paramsObj);
```

### 使用封装库
```java
// 初始化
AppLogUtils.init(this, appId);

// 事件上报
AppLogUtils.trackPageView(userId, "macBookPro", 20000, "white");
```

## 注意事项

1. 确保在使用事件上报前完成SDK初始化
2. 设备ID获取需要等待SDK注册完成
3. AB测试配置在SDK初始化后才能获取
4. 生产环境建议启用加密和关闭日志
