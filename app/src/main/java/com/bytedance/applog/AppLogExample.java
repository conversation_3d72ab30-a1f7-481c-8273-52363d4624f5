package com.bytedance.applog;

import android.app.Application;
import android.util.Log;

import com.bytedance.util.Constant;

import java.util.HashMap;
import java.util.Map;

/**
 * AppLog使用示例
 * 展示如何使用封装后的AppLog功能
 * 
 * <AUTHOR>
 */
public class AppLogExample {
    
    private static final String TAG = "AppLogExample";
    
    /**
     * 示例1：基础初始化
     */
    public static void basicInitialization(Application application) {
        // 最简单的初始化方式
        AppLogUtils.init(application, Constant.appId);
        
        // 或者使用开发环境配置
        // AppLogUtils.initDev(application, Constant.appId);
        
        // 或者使用生产环境配置
        // AppLogUtils.initProd(application, Constant.appId);
    }
    
    /**
     * 示例2：自定义配置初始化
     */
    public static void customInitialization(Application application) {
        AppLogConfig config = new AppLogConfig.Builder(Constant.appId)
                .channel("custom-channel")
                .logEnabled(true)
                .abEnabled(true)
                .encryptEnabled(false)
                .build();
        
        AppLogManager.getInstance().initialize(application, config, new AppLogManager.InitializationListener() {
            @Override
            public void onInitSuccess() {
                Log.i(TAG, "AppLog初始化成功");
                // 初始化成功后的操作
                setupABTests();
                setupCommonProperties();
            }
            
            @Override
            public void onInitFailure(Exception e) {
                Log.e(TAG, "AppLog初始化失败", e);
            }
        });
    }
    
    /**
     * 示例3：事件上报
     */
    public static void trackingEvents(String userId) {
        // 方式1：使用工具类的便捷方法
        AppLogUtils.trackPageView(userId, "MacBook Pro", 20000, "white");
        AppLogUtils.trackSearch(userId, "电脑", "/file/computers/details");
        AppLogUtils.trackAddCart(userId, "MacBook Pro", 20000, "white");
        AppLogUtils.trackPay(userId, "MacBook Pro", 19500, "creditCard");
        
        // 方式2：使用构建器模式
        AppLogUtils.event("custom_event")
                .userId(userId)
                .property("custom_prop1", "value1")
                .property("custom_prop2", 123)
                .track();
        
        // 方式3：使用事件管理器
        Map<String, Object> properties = new HashMap<>();
        properties.put("product_id", "12345");
        properties.put("category", "electronics");
        AppLogUtils.trackEvent("product_view", properties, userId);
    }
    
    /**
     * 示例4：用户属性设置
     */
    public static void setUserProperties(String userId) {
        // 方式1：使用便捷方法
        AppLogUtils.setUserProfile(userId, "张无忌", "男", 30, "乾坤大挪移");
        
        // 方式2：使用构建器模式
        AppLogUtils.userProfile(userId)
                .userName("张无忌")
                .gender("男")
                .age(30)
                .skill("乾坤大挪移")
                .property("vip_level", "gold")
                .set();
        
        // 方式3：使用Map
        Map<String, Object> userProps = new HashMap<>();
        userProps.put("user_name", "张无忌");
        userProps.put("user_gender", "男");
        userProps.put("user_age", 30);
        AppLogUtils.setUserProfile(userId, userProps);
    }
    
    /**
     * 示例5：AB测试使用
     */
    public static void setupABTests() {
        // 方式1：直接获取
        String buttonColor = AppLogUtils.getABConfig("button_color", "blue");
        boolean newFeatureEnabled = AppLogUtils.getABConfigBoolean("new_feature", false);
        int maxRetries = AppLogUtils.getABConfigInt("max_retries", 3);
        
        // 方式2：使用构建器模式
        String abValue = AppLogABTestManager.config("ab1")
                .defaultValue("default")
                .listener(new AppLogABTestManager.ABTestListener() {
                    @Override
                    public void onABTestResult(String experimentKey, String experimentValue) {
                        Log.i(TAG, "AB测试结果: " + experimentKey + " = " + experimentValue);
                        handleABTestResult(experimentKey, experimentValue);
                    }
                    
                    @Override
                    public void onABTestError(String experimentKey, String error) {
                        Log.e(TAG, "AB测试错误: " + experimentKey + " - " + error);
                    }
                })
                .get();
        
        // 方式3：使用常用实验
        if (AppLogABTestManager.CommonExperiments.isFeatureEnabled("new_ui")) {
            // 启用新UI
        }
        
        String uiVersion = AppLogABTestManager.CommonExperiments.getUIVersion();
        // 根据UI版本显示不同界面
    }
    
    /**
     * 示例6：公共属性设置
     */
    public static void setupCommonProperties() {
        Map<String, Object> commonProps = new HashMap<>();
        commonProps.put("app_version", "1.0.0");
        commonProps.put("platform", "android");
        commonProps.put("channel", "google_play");
        
        AppLogUtils.setCommonProperties(commonProps);
    }
    
    /**
     * 示例7：设备信息获取
     */
    public static void getDeviceInfo() {
        // 异步获取设备ID
        AppLogManager.getInstance().getDeviceIdAsync(new AppLogManager.DeviceIdCallback() {
            @Override
            public void onDeviceIdReady(String deviceId, String ssid) {
                Log.i(TAG, "设备ID: " + deviceId);
                Log.i(TAG, "SSID: " + ssid);
                // 使用设备ID进行后续操作
            }
            
            @Override
            public void onError(Exception e) {
                Log.e(TAG, "获取设备ID失败", e);
            }
        });
        
        // 同步获取（需要确保SDK已初始化完成）
        String deviceId = AppLogUtils.getDeviceId();
        String ssid = AppLogUtils.getSSID();
    }
    
    /**
     * 示例8：ALink监听器设置
     */
    public static void setupALinkListener() {
        AppLogManager.getInstance().setALinkListener(new AppLogManager.ALinkListener() {
            @Override
            public void onALinkData(Map<String, String> data, Exception error) {
                if (error == null && data != null) {
                    Log.i(TAG, "ALink数据: " + data.toString());
                    // 处理ALink数据
                } else {
                    Log.e(TAG, "ALink数据错误", error);
                }
            }
            
            @Override
            public void onAttributionData(Map<String, String> data, Exception error) {
                if (error == null && data != null) {
                    Log.i(TAG, "归因数据: " + data.toString());
                    // 处理归因数据
                } else {
                    Log.e(TAG, "归因数据错误", error);
                }
            }
        });
    }
    
    /**
     * 处理AB测试结果
     */
    private static void handleABTestResult(String experimentKey, String experimentValue) {
        switch (experimentKey) {
            case "ab1":
                if ("1".equals(experimentValue)) {
                    // 实验组1的逻辑
                } else if ("2".equals(experimentValue)) {
                    // 实验组2的逻辑
                } else {
                    // 对照组的逻辑
                }
                break;
            case "button_color":
                // 根据按钮颜色设置UI
                break;
            default:
                Log.d(TAG, "未处理的实验: " + experimentKey);
                break;
        }
    }
    
    /**
     * 完整的使用流程示例
     */
    public static void completeExample(Application application, String userId) {
        // 1. 初始化
        customInitialization(application);
        
        // 2. 设置用户ID
        AppLogUtils.setUserId(userId);
        
        // 3. 设置用户属性
        setUserProperties(userId);
        
        // 4. 设置公共属性
        setupCommonProperties();
        
        // 5. 上报事件
        trackingEvents(userId);
        
        // 6. 获取设备信息
        getDeviceInfo();
        
        // 7. 设置ALink监听器
        setupALinkListener();
    }
}
