package com.bytedance.applog;

import android.util.Log;

import com.bytedance.applog.AppLog;

import java.util.HashMap;
import java.util.Map;

/**
 * AppLog AB测试管理器
 * 统一管理AB测试相关功能
 * 
 * <AUTHOR>
 */
public class AppLogABTestManager {
    
    private static final String TAG = "AppLogABTestManager";
    private static AppLogABTestManager instance;
    
    // AB测试结果缓存
    private Map<String, String> abConfigCache = new HashMap<>();
    
    // AB测试监听器接口
    public interface ABTestListener {
        void onABTestResult(String experimentKey, String experimentValue);
        void onABTestError(String experimentKey, String error);
    }
    
    private AppLogABTestManager() {}
    
    public static synchronized AppLogABTestManager getInstance() {
        if (instance == null) {
            instance = new AppLogABTestManager();
        }
        return instance;
    }
    
    /**
     * 获取AB测试配置
     * 
     * @param experimentKey 实验key
     * @param defaultValue 默认值
     * @return 实验值
     */
    public String getABConfig(String experimentKey, String defaultValue) {
        try {
            String result = AppLog.getAbConfig(experimentKey, defaultValue);
            
            // 缓存结果
            abConfigCache.put(experimentKey, result);
            
            Log.d(TAG, "AB Test - Key: " + experimentKey + ", Value: " + result);
            return result;
            
        } catch (Exception e) {
            Log.e(TAG, "Error getting AB config for key: " + experimentKey, e);
            return defaultValue;
        }
    }
    
    /**
     * 获取AB测试配置（带监听器）
     */
    public String getABConfig(String experimentKey, String defaultValue, ABTestListener listener) {
        try {
            String result = getABConfig(experimentKey, defaultValue);
            
            if (listener != null) {
                listener.onABTestResult(experimentKey, result);
            }
            
            return result;
            
        } catch (Exception e) {
            Log.e(TAG, "Error getting AB config for key: " + experimentKey, e);
            
            if (listener != null) {
                listener.onABTestError(experimentKey, e.getMessage());
            }
            
            return defaultValue;
        }
    }
    
    /**
     * 获取布尔类型的AB测试配置
     */
    public boolean getABConfigBoolean(String experimentKey, boolean defaultValue) {
        String result = getABConfig(experimentKey, String.valueOf(defaultValue));
        return Boolean.parseBoolean(result);
    }
    
    /**
     * 获取整数类型的AB测试配置
     */
    public int getABConfigInt(String experimentKey, int defaultValue) {
        String result = getABConfig(experimentKey, String.valueOf(defaultValue));
        try {
            return Integer.parseInt(result);
        } catch (NumberFormatException e) {
            Log.w(TAG, "Failed to parse AB config as int: " + result + ", using default: " + defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 获取浮点数类型的AB测试配置
     */
    public double getABConfigDouble(String experimentKey, double defaultValue) {
        String result = getABConfig(experimentKey, String.valueOf(defaultValue));
        try {
            return Double.parseDouble(result);
        } catch (NumberFormatException e) {
            Log.w(TAG, "Failed to parse AB config as double: " + result + ", using default: " + defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 检查是否在实验组
     */
    public boolean isInExperimentGroup(String experimentKey, String experimentValue) {
        String currentValue = getABConfig(experimentKey, "");
        return experimentValue.equals(currentValue);
    }
    
    /**
     * 获取缓存的AB测试结果
     */
    public String getCachedABConfig(String experimentKey) {
        return abConfigCache.get(experimentKey);
    }
    
    /**
     * 清除AB测试缓存
     */
    public void clearABConfigCache() {
        abConfigCache.clear();
        Log.d(TAG, "AB config cache cleared");
    }
    
    /**
     * 获取所有缓存的AB测试配置
     */
    public Map<String, String> getAllCachedABConfigs() {
        return new HashMap<>(abConfigCache);
    }
    
    /**
     * 预加载AB测试配置
     */
    public void preloadABConfigs(String[] experimentKeys, String[] defaultValues) {
        if (experimentKeys == null || defaultValues == null || 
            experimentKeys.length != defaultValues.length) {
            Log.w(TAG, "Invalid parameters for preloading AB configs");
            return;
        }
        
        for (int i = 0; i < experimentKeys.length; i++) {
            getABConfig(experimentKeys[i], defaultValues[i]);
        }
        
        Log.d(TAG, "Preloaded " + experimentKeys.length + " AB configs");
    }
    
    /**
     * AB测试配置构建器
     */
    public static class ABConfigBuilder {
        private String experimentKey;
        private String defaultValue;
        private ABTestListener listener;
        
        public ABConfigBuilder(String experimentKey) {
            this.experimentKey = experimentKey;
        }
        
        public ABConfigBuilder defaultValue(String defaultValue) {
            this.defaultValue = defaultValue;
            return this;
        }
        
        public ABConfigBuilder listener(ABTestListener listener) {
            this.listener = listener;
            return this;
        }
        
        public String get() {
            return getInstance().getABConfig(experimentKey, defaultValue, listener);
        }
        
        public boolean getBoolean() {
            return getInstance().getABConfigBoolean(experimentKey, Boolean.parseBoolean(defaultValue));
        }
        
        public int getInt() {
            try {
                return getInstance().getABConfigInt(experimentKey, Integer.parseInt(defaultValue));
            } catch (NumberFormatException e) {
                return getInstance().getABConfigInt(experimentKey, 0);
            }
        }
        
        public double getDouble() {
            try {
                return getInstance().getABConfigDouble(experimentKey, Double.parseDouble(defaultValue));
            } catch (NumberFormatException e) {
                return getInstance().getABConfigDouble(experimentKey, 0.0);
            }
        }
    }
    
    /**
     * 创建AB配置构建器
     */
    public static ABConfigBuilder config(String experimentKey) {
        return new ABConfigBuilder(experimentKey);
    }
    
    /**
     * 常用的AB测试场景
     */
    public static class CommonExperiments {
        public static final String BUTTON_COLOR = "button_color";
        public static final String FEATURE_ENABLED = "feature_enabled";
        public static final String PRICE_STRATEGY = "price_strategy";
        public static final String UI_VERSION = "ui_version";
        
        /**
         * 检查功能是否启用
         */
        public static boolean isFeatureEnabled(String featureKey) {
            return getInstance().getABConfigBoolean(featureKey, false);
        }
        
        /**
         * 获取UI版本
         */
        public static String getUIVersion() {
            return getInstance().getABConfig(UI_VERSION, "default");
        }
        
        /**
         * 获取按钮颜色
         */
        public static String getButtonColor() {
            return getInstance().getABConfig(BUTTON_COLOR, "blue");
        }
    }
}
