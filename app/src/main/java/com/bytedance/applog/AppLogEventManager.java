package com.bytedance.applog;

import android.util.Log;

import com.bytedance.applog.AppLog;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * AppLog事件管理器
 * 统一管理所有事件上报逻辑，提供简化的API
 * 
 * <AUTHOR>
 */
public class AppLogEventManager {
    
    private static final String TAG = "AppLogEventManager";
    private static AppLogEventManager instance;
    
    // 事件名称常量
    public static final String EVENT_PAGE_VIEW = "pageView";
    public static final String EVENT_SEARCH = "search";
    public static final String EVENT_ADD_CART = "addcart";
    public static final String EVENT_PAY = "pay";
    public static final String EVENT_LOGIN = "login";
    public static final String EVENT_LOGOUT = "logout";
    public static final String EVENT_REGISTER = "register";
    
    // 事件属性常量
    public static final String PROP_COMMODITY_NAME = "commodity_name";
    public static final String PROP_PRICE = "price";
    public static final String PROP_COLOR = "color";
    public static final String PROP_SEARCH_KEYWORDS = "search_keywords";
    public static final String PROP_RESULT_DETAILS = "result_details";
    public static final String PROP_PAY_METHOD = "pay_method";
    public static final String PROP_USER_NAME = "user_name";
    public static final String PROP_USER_GENDER = "user_gender";
    public static final String PROP_USER_AGE = "user_age";
    public static final String PROP_USER_SKILL = "user_skill";
    
    private AppLogEventManager() {}
    
    public static synchronized AppLogEventManager getInstance() {
        if (instance == null) {
            instance = new AppLogEventManager();
        }
        return instance;
    }
    
    /**
     * 设置用户ID
     */
    public void setUserId(String userId) {
        if (userId != null && !userId.isEmpty()) {
            AppLog.setUserUniqueID(userId);
            Log.d(TAG, "User ID set: " + userId);
        }
    }
    
    /**
     * 上报事件（基础方法）
     */
    public void trackEvent(String eventName, Map<String, Object> properties) {
        trackEvent(eventName, properties, null);
    }
    
    /**
     * 上报事件（带用户ID）
     */
    public void trackEvent(String eventName, Map<String, Object> properties, String userId) {
        try {
            if (userId != null) {
                setUserId(userId);
            }
            
            JSONObject paramsObj = new JSONObject();
            if (properties != null) {
                for (Map.Entry<String, Object> entry : properties.entrySet()) {
                    paramsObj.put(entry.getKey(), entry.getValue());
                }
            }
            
            AppLog.onEventV3(eventName, paramsObj);
            Log.d(TAG, "Event tracked: " + eventName + " with properties: " + paramsObj.toString());
            
        } catch (JSONException e) {
            Log.e(TAG, "Error tracking event: " + eventName, e);
        }
    }
    
    /**
     * 浏览商品事件
     */
    public void trackPageView(String userId, String commodityName, double price, String color) {
        Map<String, Object> properties = new HashMap<>();
        properties.put(PROP_COMMODITY_NAME, commodityName);
        properties.put(PROP_PRICE, price);
        properties.put(PROP_COLOR, color);
        
        trackEvent(EVENT_PAGE_VIEW, properties, userId);
    }
    
    /**
     * 搜索事件
     */
    public void trackSearch(String userId, String keywords, String resultDetails) {
        Map<String, Object> properties = new HashMap<>();
        properties.put(PROP_SEARCH_KEYWORDS, keywords);
        properties.put(PROP_RESULT_DETAILS, resultDetails);
        
        trackEvent(EVENT_SEARCH, properties, userId);
    }
    
    /**
     * 添加购物车事件
     */
    public void trackAddCart(String userId, String commodityName, double price, String color) {
        Map<String, Object> properties = new HashMap<>();
        properties.put(PROP_COMMODITY_NAME, commodityName);
        properties.put(PROP_PRICE, price);
        properties.put(PROP_COLOR, color);
        
        trackEvent(EVENT_ADD_CART, properties, userId);
    }
    
    /**
     * 支付事件
     */
    public void trackPay(String userId, String commodityName, double price, String payMethod) {
        if (userId == null) {
            Log.w(TAG, "Pay event requires user ID");
            return;
        }
        
        Map<String, Object> properties = new HashMap<>();
        properties.put(PROP_COMMODITY_NAME, commodityName);
        properties.put(PROP_PRICE, price);
        properties.put(PROP_PAY_METHOD, payMethod);
        
        trackEvent(EVENT_PAY, properties, userId);
    }
    
    /**
     * 登录事件
     */
    public void trackLogin(String userId, String loginMethod) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("login_method", loginMethod);
        
        trackEvent(EVENT_LOGIN, properties, userId);
    }
    
    /**
     * 登出事件
     */
    public void trackLogout(String userId) {
        trackEvent(EVENT_LOGOUT, null, userId);
    }
    
    /**
     * 注册事件
     */
    public void trackRegister(String userId, String registerMethod) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("register_method", registerMethod);
        
        trackEvent(EVENT_REGISTER, properties, userId);
    }
    
    /**
     * 设置用户属性
     */
    public void setUserProfile(String userId, Map<String, Object> userProperties) {
        if (userId == null) {
            Log.w(TAG, "User profile requires user ID");
            return;
        }
        
        try {
            setUserId(userId);
            
            JSONObject paramsObj = new JSONObject();
            if (userProperties != null) {
                for (Map.Entry<String, Object> entry : userProperties.entrySet()) {
                    paramsObj.put(entry.getKey(), entry.getValue());
                }
            }
            
            AppLog.profileSet(paramsObj);
            Log.d(TAG, "User profile set for user: " + userId + " with properties: " + paramsObj.toString());
            
        } catch (JSONException e) {
            Log.e(TAG, "Error setting user profile for user: " + userId, e);
        }
    }
    
    /**
     * 设置用户属性（便捷方法）
     */
    public void setUserProfile(String userId, String userName, String gender, int age, String skill) {
        Map<String, Object> properties = new HashMap<>();
        properties.put(PROP_USER_NAME, userName);
        properties.put(PROP_USER_GENDER, gender);
        properties.put(PROP_USER_AGE, age);
        properties.put(PROP_USER_SKILL, skill);
        
        setUserProfile(userId, properties);
    }
    
    /**
     * 设置事件公共属性
     */
    public void setCommonProperties(Map<String, Object> commonProperties) {
        if (commonProperties != null) {
            AppLog.setHeaderInfo((HashMap<String, Object>) commonProperties);
            Log.d(TAG, "Common properties set: " + commonProperties.toString());
        }
    }
    
    /**
     * 清除事件公共属性
     */
    public void clearCommonProperties() {
        AppLog.setHeaderInfo(new HashMap<String, Object>());
        Log.d(TAG, "Common properties cleared");
    }
}
