package com.bytedance.applog;

import android.app.Application;
import android.util.Log;

import com.bytedance.util.Constant;

/**
 * AppLog封装库测试类
 * 用于验证封装功能是否正常工作
 * 
 * <AUTHOR>
 */
public class AppLogTest {
    
    private static final String TAG = "AppLogTest";
    
    /**
     * 测试基础功能
     */
    public static void testBasicFunctions(Application application) {
        Log.i(TAG, "开始测试AppLog封装库基础功能");
        
        // 1. 测试初始化
        testInitialization(application);
        
        // 2. 测试事件上报
        testEventTracking();
        
        // 3. 测试用户属性
        testUserProfile();
        
        // 4. 测试AB测试
        testABTesting();
        
        // 5. 测试设备信息
        testDeviceInfo();
        
        Log.i(TAG, "AppLog封装库基础功能测试完成");
    }
    
    /**
     * 测试初始化功能
     */
    private static void testInitialization(Application application) {
        Log.i(TAG, "测试初始化功能");
        
        try {
            // 测试快速初始化
            AppLogUtils.init(application, Constant.appId);
            Log.i(TAG, "✓ 快速初始化成功");
            
            // 测试自定义配置初始化
            AppLogConfig config = new AppLogConfig.Builder(Constant.appId)
                    .channel("test-channel")
                    .logEnabled(true)
                    .abEnabled(true)
                    .build();
            
            Log.i(TAG, "✓ 自定义配置创建成功");
            
        } catch (Exception e) {
            Log.e(TAG, "✗ 初始化测试失败", e);
        }
    }
    
    /**
     * 测试事件上报功能
     */
    private static void testEventTracking() {
        Log.i(TAG, "测试事件上报功能");
        
        try {
            String testUserId = "test_user_123";
            
            // 测试便捷方法
            AppLogUtils.trackPageView(testUserId, "测试商品", 999.99, "红色");
            AppLogUtils.trackSearch(testUserId, "测试搜索", "/test/search");
            AppLogUtils.trackAddCart(testUserId, "测试商品", 999.99, "红色");
            
            Log.i(TAG, "✓ 便捷方法事件上报成功");
            
            // 测试构建器模式
            AppLogUtils.event("test_custom_event")
                    .userId(testUserId)
                    .property("test_prop1", "value1")
                    .property("test_prop2", 123)
                    .track();
            
            Log.i(TAG, "✓ 构建器模式事件上报成功");
            
        } catch (Exception e) {
            Log.e(TAG, "✗ 事件上报测试失败", e);
        }
    }
    
    /**
     * 测试用户属性功能
     */
    private static void testUserProfile() {
        Log.i(TAG, "测试用户属性功能");
        
        try {
            String testUserId = "test_user_123";
            
            // 测试便捷方法
            AppLogUtils.setUserProfile(testUserId, "测试用户", "男", 25, "测试技能");
            Log.i(TAG, "✓ 便捷方法用户属性设置成功");
            
            // 测试构建器模式
            AppLogUtils.userProfile(testUserId)
                    .userName("测试用户2")
                    .gender("女")
                    .age(30)
                    .property("city", "北京")
                    .set();
            
            Log.i(TAG, "✓ 构建器模式用户属性设置成功");
            
        } catch (Exception e) {
            Log.e(TAG, "✗ 用户属性测试失败", e);
        }
    }
    
    /**
     * 测试AB测试功能
     */
    private static void testABTesting() {
        Log.i(TAG, "测试AB测试功能");
        
        try {
            // 测试基础AB配置获取
            String abValue = AppLogUtils.getABConfig("test_ab", "default");
            boolean abBoolean = AppLogUtils.getABConfigBoolean("test_feature", false);
            int abInt = AppLogUtils.getABConfigInt("test_number", 10);
            
            Log.i(TAG, "✓ AB配置获取成功: " + abValue + ", " + abBoolean + ", " + abInt);
            
            // 测试构建器模式
            AppLogABTestManager.config("test_ab_builder")
                    .defaultValue("builder_default")
                    .listener(new AppLogABTestManager.ABTestListener() {
                        @Override
                        public void onABTestResult(String experimentKey, String experimentValue) {
                            Log.i(TAG, "✓ AB测试监听器回调成功: " + experimentKey + " = " + experimentValue);
                        }
                        
                        @Override
                        public void onABTestError(String experimentKey, String error) {
                            Log.w(TAG, "AB测试错误: " + experimentKey + " - " + error);
                        }
                    })
                    .get();
            
            Log.i(TAG, "✓ AB测试构建器模式成功");
            
        } catch (Exception e) {
            Log.e(TAG, "✗ AB测试功能测试失败", e);
        }
    }
    
    /**
     * 测试设备信息功能
     */
    private static void testDeviceInfo() {
        Log.i(TAG, "测试设备信息功能");
        
        try {
            // 测试异步获取设备信息
            AppLogManager.getInstance().getDeviceIdAsync(new AppLogManager.DeviceIdCallback() {
                @Override
                public void onDeviceIdReady(String deviceId, String ssid) {
                    Log.i(TAG, "✓ 异步获取设备信息成功: DID=" + deviceId + ", SSID=" + ssid);
                }
                
                @Override
                public void onError(Exception e) {
                    Log.w(TAG, "设备信息获取失败", e);
                }
            });
            
            // 测试同步获取（可能为空，因为需要等待初始化完成）
            String deviceId = AppLogUtils.getDeviceId();
            String ssid = AppLogUtils.getSSID();
            Log.i(TAG, "同步获取设备信息: DID=" + deviceId + ", SSID=" + ssid);
            
        } catch (Exception e) {
            Log.e(TAG, "✗ 设备信息测试失败", e);
        }
    }
    
    /**
     * 测试配置功能
     */
    public static void testConfiguration() {
        Log.i(TAG, "测试配置功能");
        
        try {
            // 测试默认配置
            AppLogConfig defaultConfig = AppLogConfig.createDefault("test_app_id");
            Log.i(TAG, "✓ 默认配置创建成功");
            
            // 测试开发环境配置
            AppLogConfig devConfig = AppLogConfig.createDevelopment("test_app_id");
            Log.i(TAG, "✓ 开发环境配置创建成功");
            
            // 测试生产环境配置
            AppLogConfig prodConfig = AppLogConfig.createProduction("test_app_id");
            Log.i(TAG, "✓ 生产环境配置创建成功");
            
            // 测试自定义配置
            AppLogConfig customConfig = new AppLogConfig.Builder("test_app_id")
                    .channel("custom_channel")
                    .logEnabled(true)
                    .abEnabled(true)
                    .encryptEnabled(false)
                    .autoStart(true)
                    .build();
            
            Log.i(TAG, "✓ 自定义配置创建成功");
            
            // 验证配置属性
            assert customConfig.getAppId().equals("test_app_id");
            assert customConfig.getChannel().equals("custom_channel");
            assert customConfig.isLogEnabled();
            assert customConfig.isAbEnabled();
            assert !customConfig.isEncryptEnabled();
            assert customConfig.isAutoStart();
            
            Log.i(TAG, "✓ 配置属性验证成功");
            
        } catch (Exception e) {
            Log.e(TAG, "✗ 配置功能测试失败", e);
        }
    }
    
    /**
     * 测试管理器状态
     */
    public static void testManagerStatus() {
        Log.i(TAG, "测试管理器状态");
        
        try {
            // 测试单例模式
            AppLogManager manager1 = AppLogManager.getInstance();
            AppLogManager manager2 = AppLogManager.getInstance();
            assert manager1 == manager2;
            Log.i(TAG, "✓ AppLogManager单例模式正常");
            
            AppLogEventManager eventManager1 = AppLogEventManager.getInstance();
            AppLogEventManager eventManager2 = AppLogEventManager.getInstance();
            assert eventManager1 == eventManager2;
            Log.i(TAG, "✓ AppLogEventManager单例模式正常");
            
            AppLogABTestManager abManager1 = AppLogABTestManager.getInstance();
            AppLogABTestManager abManager2 = AppLogABTestManager.getInstance();
            assert abManager1 == abManager2;
            Log.i(TAG, "✓ AppLogABTestManager单例模式正常");
            
            // 测试初始化状态
            boolean isInitialized = manager1.isInitialized();
            Log.i(TAG, "AppLog初始化状态: " + isInitialized);
            
        } catch (Exception e) {
            Log.e(TAG, "✗ 管理器状态测试失败", e);
        }
    }
    
    /**
     * 运行所有测试
     */
    public static void runAllTests(Application application) {
        Log.i(TAG, "=== 开始运行AppLog封装库完整测试 ===");
        
        testConfiguration();
        testManagerStatus();
        testBasicFunctions(application);
        
        Log.i(TAG, "=== AppLog封装库完整测试结束 ===");
    }
}
