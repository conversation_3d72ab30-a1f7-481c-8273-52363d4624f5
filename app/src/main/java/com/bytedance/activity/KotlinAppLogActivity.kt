package com.bytedance.activity

import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.bytedance.R
import com.bytedance.applog.kotlin.*
import com.bytedance.global.MyApplication
import kotlinx.coroutines.launch

/**
 * Kotlin版本的AppLog使用示例Activity
 * 展示如何使用Kotlin封装后的AppLog功能
 * 
 * <AUTHOR>
 */
class KotlinAppLogActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "KotlinAppLogActivity"
    }
    
    private val application by lazy { getApplication() as MyApplication }
    private val userId by lazy { application.userId }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_kotlin_applog)
        
        initViews()
        setupCommonProperties()
        testABExperiments()
    }
    
    private fun initViews() {
        // 使用Kotlin的简洁语法设置点击事件
        findViewById<android.widget.Button>(R.id.btn_track_page_view_kt).setOnClickListener { trackPageView() }
        findViewById<android.widget.Button>(R.id.btn_track_search_kt).setOnClickListener { trackSearch() }
        findViewById<android.widget.Button>(R.id.btn_track_add_cart_kt).setOnClickListener { trackAddCart() }
        findViewById<android.widget.Button>(R.id.btn_track_pay_kt).setOnClickListener { trackPay() }
        findViewById<android.widget.Button>(R.id.btn_set_user_profile_kt).setOnClickListener { setUserProfile() }
        findViewById<android.widget.Button>(R.id.btn_custom_event_kt).setOnClickListener { trackCustomEvent() }
        findViewById<android.widget.Button>(R.id.btn_get_device_info_kt).setOnClickListener { getDeviceInfo() }
        findViewById<android.widget.Button>(R.id.btn_test_ab_kt).setOnClickListener { testABConfig() }
        findViewById<android.widget.Button>(R.id.btn_test_extensions_kt).setOnClickListener { testExtensions() }
        findViewById<android.widget.Button>(R.id.btn_test_dsl_kt).setOnClickListener { testDSL() }
    }
    
    /**
     * 设置公共属性 - 使用Kotlin扩展函数
     */
    private fun setupCommonProperties() {
        mapOf(
            "app_version" to "2.0.0-kotlin",
            "platform" to "android",
            "page_name" to "KotlinAppLogActivity",
            "language" to "kotlin"
        ).setAsCommonProperties()
        
        Log.i(TAG, "公共属性设置完成")
    }
    
    /**
     * 浏览商品事件 - 展示多种Kotlin用法
     */
    private fun trackPageView() {
        // 方式1：使用便捷方法
        AppLogUtils.trackPageView(userId, "iPhone 14 Pro Max", 9999.0, "紫色")
        
        // 方式2：使用DSL风格
        trackEvent("page_view_detailed") {
            userId(userId)
            commodityName("iPhone 14 Pro Max")
            price(9999.0)
            color("紫色")
            property("category", "手机")
            property("brand", "Apple")
            property("storage", "1TB")
        }
        
        // 方式3：使用扩展函数
        "iPhone 14 Pro Max".trackAsPageView(userId, 9999.0, "紫色")
        
        showToast("浏览商品事件已上报（Kotlin版本）")
    }
    
    /**
     * 搜索事件 - 使用扩展函数
     */
    private fun trackSearch() {
        "iPhone".trackAsSearch(userId, "/search/results/iphone")
        
        // 或者使用DSL
        trackEvent("search_advanced") {
            userId(userId)
            searchKeywords("iPhone")
            resultDetails("/search/results/iphone")
            property("search_type", "product")
            property("result_count", 156)
        }
        
        showToast("搜索事件已上报")
    }
    
    /**
     * 添加购物车事件
     */
    private fun trackAddCart() {
        AppLogUtils.trackAddCart(userId, "iPhone 14 Pro Max", 9999.0, "紫色")
        showToast("添加购物车事件已上报")
    }
    
    /**
     * 支付事件 - 带空值检查
     */
    private fun trackPay() {
        userId?.let { id ->
            AppLogUtils.trackPay(id, "iPhone 14 Pro Max", 9999.0, "支付宝")
            showToast("支付事件已上报")
        } ?: showToast("请先登录")
    }
    
    /**
     * 设置用户属性 - 使用DSL风格
     */
    private fun setUserProfile() {
        userId?.let { id ->
            // 方式1：使用便捷方法
            AppLogUtils.setUserProfile(id, "李小红", "女", 26, "Kotlin开发")
            
            // 方式2：使用DSL风格
            setUserProfile(id) {
                userName("李小红")
                gender("女")
                age(26)
                skill("Kotlin开发")
                property("city", "深圳")
                property("education", "硕士")
                property("vip_level", "platinum")
                property("interests", listOf("编程", "旅游", "摄影"))
            }
            
            showToast("用户属性已设置")
        } ?: showToast("请先登录")
    }
    
    /**
     * 自定义事件 - 使用DSL和扩展函数
     */
    private fun trackCustomEvent() {
        // 使用DSL风格
        trackEvent("kotlin_custom_event") {
            userId(userId)
            property("button_name", "custom_event_button")
            property("click_time", System.currentTimeMillis())
            property("page_source", "KotlinAppLogActivity")
            property("kotlin_features", listOf("dsl", "extensions", "coroutines"))
        }
        
        // 条件事件追踪
        trackEventIf(userId != null, "user_interaction") {
            userId(userId)
            property("interaction_type", "button_click")
            property("feature", "kotlin_dsl")
        }
        
        showToast("自定义事件已上报")
    }
    
    /**
     * 获取设备信息 - 使用协程
     */
    private fun getDeviceInfo() {
        lifecycleScope.launch {
            try {
                val (deviceId, ssid) = getDeviceInfoAsync()
                val info = "设备ID: $deviceId\nSSID: $ssid"
                showToast(info)
                Log.i(TAG, info)
            } catch (e: Exception) {
                val error = "获取设备信息失败: ${e.message}"
                showToast(error)
                Log.e(TAG, error, e)
            }
        }
    }
    
    /**
     * 测试AB配置 - 展示多种Kotlin用法
     */
    private fun testABConfig() {
        // 方式1：使用扩展函数
        val buttonColor = "button_color".asABConfig("blue")
        val newFeatureEnabled = "new_feature".asABConfigBoolean(false)
        val maxRetries = "max_retries".asABConfigInt(3)
        
        // 方式2：使用高阶函数
        withABTest("ui_theme", "light") { theme ->
            Log.i(TAG, "UI主题: $theme")
        }
        
        // 方式3：使用DSL风格
        abConfig("feature_flag") {
            defaultValue(false)
            listener(object : AppLogABTestManager.ABTestListener {
                override fun onABTestResult(experimentKey: String, experimentValue: String) {
                    Log.i(TAG, "AB测试结果: $experimentKey = $experimentValue")
                }
                
                override fun onABTestError(experimentKey: String, error: String) {
                    Log.e(TAG, "AB测试错误: $experimentKey - $error")
                }
            })
        }.getBoolean()
        
        val result = """
            按钮颜色: $buttonColor
            新功能启用: $newFeatureEnabled
            最大重试次数: $maxRetries
        """.trimIndent()
        
        showToast(result)
        Log.i(TAG, "AB测试配置: $result")
    }
    
    /**
     * 测试Kotlin扩展函数
     */
    private fun testExtensions() {
        // 测试用户ID扩展函数
        userId?.setAsUserId()
        
        // 测试商品扩展函数
        "MacBook Pro".trackAsPageView(userId, 15999.0, "银色")
        "MacBook".trackAsSearch(userId, "/search/macbook")
        
        // 测试AB配置扩展函数
        val theme = "app_theme".asABConfig("default")
        val enabled = "feature_enabled".asABConfigBoolean(false)
        
        Log.i(TAG, "扩展函数测试: theme=$theme, enabled=$enabled")
        showToast("扩展函数测试完成")
    }
    
    /**
     * 测试DSL功能
     */
    private fun testDSL() {
        // 测试事件DSL
        trackEvent("dsl_test_event") {
            userId(userId)
            property("test_type", "dsl")
            property("features", listOf("builder", "lambda", "extension"))
            commodityName("测试商品")
            price(100.0)
        }
        
        // 测试用户属性DSL
        userId?.let { id ->
            setUserProfile(id) {
                userName("DSL测试用户")
                age(30)
                property("test_framework", "kotlin_dsl")
                property("preferences", mapOf(
                    "language" to "kotlin",
                    "framework" to "android"
                ))
            }
        }
        
        // 测试批量事件
        trackEvents(
            "dsl_event_1" to mapOf("prop1" to "value1"),
            "dsl_event_2" to mapOf("prop2" to "value2"),
            "dsl_event_3" to null
        )
        
        showToast("DSL功能测试完成")
    }
    
    /**
     * 测试AB实验 - 使用协程
     */
    private fun testABExperiments() {
        lifecycleScope.launch {
            try {
                // 使用高阶函数测试AB
                withABTest("kotlin_ui_theme", "light") { theme ->
                    applyUITheme(theme)
                }
                
                // 检查功能开关
                if (AppLogABTestManager.CommonExperiments.isFeatureEnabled("kotlin_new_features")) {
                    Log.i(TAG, "Kotlin新功能已启用")
                    enableKotlinFeatures()
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "AB实验测试失败", e)
            }
        }
    }
    
    /**
     * 应用UI主题
     */
    private fun applyUITheme(theme: String) {
        Log.i(TAG, "应用UI主题: $theme")
        when (theme) {
            "dark" -> {
                // 应用暗色主题
                Log.i(TAG, "切换到暗色主题")
            }
            "light" -> {
                // 应用亮色主题
                Log.i(TAG, "切换到亮色主题")
            }
            else -> {
                Log.i(TAG, "使用默认主题")
            }
        }
    }
    
    /**
     * 启用Kotlin特性
     */
    private fun enableKotlinFeatures() {
        Log.i(TAG, "启用Kotlin特性: DSL, 扩展函数, 协程")
    }
    
    /**
     * 显示Toast消息
     */
    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    override fun onResume() {
        super.onResume()
        // 页面浏览事件 - 使用DSL
        trackEvent("page_enter") {
            userId(userId)
            property("page_name", "KotlinAppLogActivity")
            property("enter_time", System.currentTimeMillis())
            property("language", "kotlin")
        }
    }
    
    override fun onPause() {
        super.onPause()
        // 页面离开事件 - 使用DSL
        trackEvent("page_leave") {
            userId(userId)
            property("page_name", "KotlinAppLogActivity")
            property("leave_time", System.currentTimeMillis())
            property("language", "kotlin")
        }
    }
}
