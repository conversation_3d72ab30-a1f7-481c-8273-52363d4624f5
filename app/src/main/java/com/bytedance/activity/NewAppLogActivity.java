package com.bytedance.activity;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.bytedance.R;
import com.bytedance.applog.AppLogUtils;
import com.bytedance.applog.AppLogABTestManager;
import com.bytedance.applog.AppLogManager;
import com.bytedance.global.MyApplication;

import java.util.HashMap;
import java.util.Map;

/**
 * 新的AppLog使用示例Activity
 * 展示如何使用封装后的AppLog功能
 * 
 * <AUTHOR>
 */
public class NewAppLogActivity extends AppCompatActivity {
    
    private static final String TAG = "NewAppLogActivity";
    private MyApplication application;
    private String userId;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_new_applog);
        
        application = (MyApplication) getApplication();
        userId = application.getUserId();
        
        initViews();
        setupCommonProperties();
        testABExperiments();
    }
    
    private void initViews() {
        // 设置按钮点击事件
        findViewById(R.id.btn_track_page_view).setOnClickListener(this::trackPageView);
        findViewById(R.id.btn_track_search).setOnClickListener(this::trackSearch);
        findViewById(R.id.btn_track_add_cart).setOnClickListener(this::trackAddCart);
        findViewById(R.id.btn_track_pay).setOnClickListener(this::trackPay);
        findViewById(R.id.btn_set_user_profile).setOnClickListener(this::setUserProfile);
        findViewById(R.id.btn_custom_event).setOnClickListener(this::trackCustomEvent);
        findViewById(R.id.btn_get_device_info).setOnClickListener(this::getDeviceInfo);
        findViewById(R.id.btn_test_ab).setOnClickListener(this::testABConfig);
    }
    
    /**
     * 设置公共属性
     */
    private void setupCommonProperties() {
        Map<String, Object> commonProps = new HashMap<>();
        commonProps.put("app_version", "2.0.0");
        commonProps.put("platform", "android");
        commonProps.put("page_name", "NewAppLogActivity");
        
        AppLogUtils.setCommonProperties(commonProps);
        Log.i(TAG, "公共属性设置完成");
    }
    
    /**
     * 浏览商品事件
     */
    public void trackPageView(View view) {
        // 方式1：使用便捷方法
        AppLogUtils.trackPageView(userId, "iPhone 14 Pro", 8999, "深空黑");
        
        // 方式2：使用构建器模式
        AppLogUtils.event("page_view_detailed")
                .userId(userId)
                .commodityName("iPhone 14 Pro")
                .price(8999)
                .color("深空黑")
                .property("category", "手机")
                .property("brand", "Apple")
                .track();
        
        showToast("浏览商品事件已上报");
    }
    
    /**
     * 搜索事件
     */
    public void trackSearch(View view) {
        AppLogUtils.trackSearch(userId, "iPhone", "/search/results/iphone");
        showToast("搜索事件已上报");
    }
    
    /**
     * 添加购物车事件
     */
    public void trackAddCart(View view) {
        AppLogUtils.trackAddCart(userId, "iPhone 14 Pro", 8999, "深空黑");
        showToast("添加购物车事件已上报");
    }
    
    /**
     * 支付事件
     */
    public void trackPay(View view) {
        if (userId == null) {
            showToast("请先登录");
            return;
        }
        
        AppLogUtils.trackPay(userId, "iPhone 14 Pro", 8999, "支付宝");
        showToast("支付事件已上报");
    }
    
    /**
     * 设置用户属性
     */
    public void setUserProfile(View view) {
        if (userId == null) {
            showToast("请先登录");
            return;
        }
        
        // 方式1：使用便捷方法
        AppLogUtils.setUserProfile(userId, "李小明", "男", 28, "Android开发");
        
        // 方式2：使用构建器模式
        AppLogUtils.userProfile(userId)
                .userName("李小明")
                .gender("男")
                .age(28)
                .skill("Android开发")
                .property("city", "北京")
                .property("education", "本科")
                .property("vip_level", "gold")
                .set();
        
        showToast("用户属性已设置");
    }
    
    /**
     * 自定义事件
     */
    public void trackCustomEvent(View view) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("button_name", "custom_event_button");
        properties.put("click_time", System.currentTimeMillis());
        properties.put("page_source", "NewAppLogActivity");
        
        AppLogUtils.trackEvent("custom_button_click", properties, userId);
        showToast("自定义事件已上报");
    }
    
    /**
     * 获取设备信息
     */
    public void getDeviceInfo(View view) {
        // 异步获取设备信息
        AppLogManager.getInstance().getDeviceIdAsync(new AppLogManager.DeviceIdCallback() {
            @Override
            public void onDeviceIdReady(String deviceId, String ssid) {
                runOnUiThread(() -> {
                    String info = "设备ID: " + deviceId + "\nSSID: " + ssid;
                    showToast(info);
                    Log.i(TAG, info);
                });
            }
            
            @Override
            public void onError(Exception e) {
                runOnUiThread(() -> {
                    showToast("获取设备信息失败: " + e.getMessage());
                    Log.e(TAG, "获取设备信息失败", e);
                });
            }
        });
    }
    
    /**
     * 测试AB配置
     */
    public void testABConfig(View view) {
        // 测试不同类型的AB配置
        String buttonColor = AppLogUtils.getABConfig("button_color", "blue");
        boolean newFeatureEnabled = AppLogUtils.getABConfigBoolean("new_feature", false);
        int maxRetries = AppLogUtils.getABConfigInt("max_retries", 3);
        
        String result = String.format("按钮颜色: %s\n新功能启用: %s\n最大重试次数: %d", 
                buttonColor, newFeatureEnabled, maxRetries);
        
        showToast(result);
        Log.i(TAG, "AB测试配置: " + result);
    }
    
    /**
     * 测试AB实验
     */
    private void testABExperiments() {
        // 使用监听器获取AB配置
        AppLogABTestManager.config("ui_theme")
                .defaultValue("light")
                .listener(new AppLogABTestManager.ABTestListener() {
                    @Override
                    public void onABTestResult(String experimentKey, String experimentValue) {
                        Log.i(TAG, "AB实验结果: " + experimentKey + " = " + experimentValue);
                        applyUITheme(experimentValue);
                    }
                    
                    @Override
                    public void onABTestError(String experimentKey, String error) {
                        Log.e(TAG, "AB实验错误: " + experimentKey + " - " + error);
                        applyUITheme("light"); // 使用默认主题
                    }
                })
                .get();
        
        // 检查功能开关
        if (AppLogABTestManager.CommonExperiments.isFeatureEnabled("new_ui_components")) {
            Log.i(TAG, "新UI组件功能已启用");
            // 启用新UI组件
        }
    }
    
    /**
     * 应用UI主题
     */
    private void applyUITheme(String theme) {
        Log.i(TAG, "应用UI主题: " + theme);
        // 根据主题设置UI
        if ("dark".equals(theme)) {
            // 应用暗色主题
        } else {
            // 应用亮色主题
        }
    }
    
    /**
     * 显示Toast消息
     */
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 页面浏览事件
        AppLogUtils.event("page_enter")
                .userId(userId)
                .property("page_name", "NewAppLogActivity")
                .property("enter_time", System.currentTimeMillis())
                .track();
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        // 页面离开事件
        AppLogUtils.event("page_leave")
                .userId(userId)
                .property("page_name", "NewAppLogActivity")
                .property("leave_time", System.currentTimeMillis())
                .track();
    }
}
