package com.bytedance.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.bytedance.event.Event;
import com.bytedance.global.MyApplication;
import com.bytedance.R;
import com.bytedance.applog.AppLog;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;


/**
 * desc:登录后功能页面
 * author: allen
 */
public class Login extends AppCompatActivity {


    private MyApplication application ;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.login);

        application = (MyApplication) this.getApplication();
        TextView userId = findViewById(R.id.userId);
        userId.setText("用户:"+application.getUserId());

        public_prop();
    }

    /**
     * 上传事件公共属性
     */
    public void public_prop() {
        Map<String,Object> headerMap = new HashMap<String, Object>();
        headerMap.put("public_prop1","公共属性值1");
        headerMap.put("public_prop2","公共属性值2");
        AppLog.setHeaderInfo((HashMap<String, Object>)headerMap);
    }


    public void public_prop2() {
        Map<String,Object> headerMap = new HashMap<String, Object>();
        headerMap.put("public_prop1","公共属性值A");
        headerMap.put("public_prop2","公共属性值B");
        AppLog.setHeaderInfo((HashMap<String, Object>)headerMap);
    }

    public void pageView(View view) {
        System.out.println("--pageView--->"+application.getUserId());
        Event event = new Event();
        event.pageView(application.getUserId());
    }

    public void search(View view) {
        System.out.println("--search--->"+application.getUserId());
        Event event = new Event();
        event.search(application.getUserId());
    }

    public void addcart(View view) {
        System.out.println("--addcart--->"+application.getUserId());
        Event event = new Event();
        event.addcart(application.getUserId());

        public_prop2();
    }

    public void pay(View view) {
        System.out.println("--pay--->"+application.getUserId());
        Event event = new Event();
        event.pay(application.getUserId());
    }


    public void userEdit(View view) {
        application = (MyApplication) this.getApplication();
        System.out.println("--userEdit--->"+application.getUserId());
        Event event = new Event();
        event.userEdit(application.getUserId());
    }

    public void logout(View view) {
        application = (MyApplication) this.getApplication();
        System.out.println("--logout--->"+application.getUserId());
        application.setUserId(null);
        Intent intent = new Intent(this, MainActivity.class);
        startActivity(intent);
    }






}