package com.bytedance.activity;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import com.bytedance.applog.UriConfig;
import com.bytedance.applog.alink.IALinkListener;
import com.bytedance.global.MyApplication;
import com.bytedance.R;
import com.bytedance.applog.AppLog;
import com.bytedance.applog.ILogger;
import com.bytedance.applog.InitConfig;
import com.bytedance.applog.picker.Picker;
import com.bytedance.applog.util.UriConstants;
import com.bytedance.util.Constant;

import java.util.Arrays;
import java.util.Map;


/**
 * desc:主页面
 * author: allen
 */
public class MainActivity extends AppCompatActivity {

    private MyApplication application;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        //sdk初始化
        sdkInit();
    }

    /**
     * SDK初始化
     */
    private void sdkInit() {

        //appid和渠道，appid如不清楚请联系客户成功经理，注意第二个参数 channel 不能为空
        final InitConfig config = new InitConfig(Constant.appId, "test-new");
        // 设置分流地址，各环境各地域的分流地址请参见上文的 获取数据上送地址 章节。

        // https://www.volcengine.com/docs/6287/1544684
        UriConfig uriConfig = UriConfig.createByDomain("https://gator.uba.ap-southeast-1.volces.com", null);
        // 设置分流地址，各环境各地域的分流地址请参见上文的 获取数据上送地址 章节。
        uriConfig.setAbUri("https://tab.ab.ap-southeast-1.volces.com" + UriConfig.PATH_AB);

        config.setUriConfig(uriConfig);
//        config.setUriConfig(UriConstants.DEFAULT);//saas上报地址
        config.setAbEnable(true); // 开启 AB 测试
        // 是否在控制台输出日志，可用于观察用户行为日志上报情况，上线之前可去掉
        config.setLogEnable(true);
        config.setLogger(new ILogger() {
            @Override
            public void log(String s, Throwable throwable) {
                Log.d("AppLog------->: ", "" + s);
            }
        });
        config.setPicker(new Picker(this.getApplication(), config)); // 开启圈选埋点
        config.setAutoTrackEnabled(true); //开启圈选预置事件开关，true开启，false关闭
        //开启Bridge开关，打通内嵌H5
        config.setH5BridgeEnable(true);
        //设置白名单，将自定义url添加到白名单
        config.setH5BridgeAllowlist(Arrays.asList("*.bytedance.*", "*.pstatp.*"));

        config.setH5CollectEnable(true);//关闭内嵌H5页面的无埋点事件
        config.setAutoTrackFragmentEnabled(true);
        // 加密开关，SDK 5.5.1 及以上版本支持，false 为关闭加密，上线前建议设置为 true
        AppLog.setEncryptAndCompress(false);

        //如需要广告检测相关功能，请详看官网文档
        tracer(config);
        config.setAutoStart(true);
        //如果打开app,就可以获取到用户ID的场景,请在初始化的时候进行设置
//        config.setUserUniqueId("${UserId}");
        AppLog.init(this, config);

        try {
            //获取设备id,需要等设备注册完成之后
            Thread.sleep(1200);
            queryDeviceId();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        /**
         * 在哪做实验在哪调用getabconfig，并且增加监听方法，ab1为实验key，null为默认兜底值
         */
        String newAb1 = AppLog.getAbConfig("ab1", "null");
        Log.i("---测试---",""+newAb1);
        System.out.println("malijia: 实验值 = "+newAb1);
        if (newAb1.equals("1")) {
            //
        } else if (newAb1.equals("2")) {
            //
        } else {
            //
        }
    }
    /**
     * 广告监测集成
     * 无广告检测需要,请忽略
     */
    public void tracer(InitConfig config)   {
        //启用延迟深度链接
        config.enableDeferredALink();
        //配合h5开启剪切板
        AppLog.setClipboardEnabled(true);
        AppLog.setALinkListener(new IALinkListener() {
            @Override
            public void onALinkData(@Nullable Map<String, String> map, @Nullable Exception e) {
                //TODO 业务逻辑
                Log.i("AppLog----onALinkData", map.toString());
            }
            @Override
            public void onAttributionData(@Nullable Map<String, String> map, @Nullable Exception e) {
                //TODO 业务逻辑
                Log.i("AppLog----onAttribution", map.toString());
            }
        });
    }

    /**
     * 高阶使用- 获取设备id&ssid
     */
    public void queryDeviceId() {
        Log.i("AppLog-------did---->:", AppLog.getDid());
        Log.i("AppLog-------ssid---->:", AppLog.getSsid());
    }

    /**
     * 匿名访问 跳转到子页面
     *
     * @param view
     */
    public void anonymous(View view) {
        Intent intent = new Intent(this, Anonymous.class);
        startActivity(intent);
    }


    /**
     * A用户登录
     *
     * @param view
     */
    public void userLoginA(View view) {
        application = (MyApplication) this.getApplication();
        application.setUserId(Constant.userA);
        System.out.println("----A用户登录访问--->" + application.getUserId());
        Intent intent = new Intent(this, Login.class);
        startActivity(intent);
    }

    /**
     * B用户登录
     *
     * @param view
     */
    public void userLoginB(View view) {
        application = (MyApplication) this.getApplication();
        application.setUserId(Constant.userB);
        System.out.println("----B用户登录访问--->" + application.getUserId());
        Intent intent = new Intent(this, Login.class);
        startActivity(intent);
    }

}