package com.bytedance.activity;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import com.bytedance.applog.UriConfig;
import com.bytedance.applog.alink.IALinkListener;
import com.bytedance.global.MyApplication;
import com.bytedance.R;
import com.bytedance.applog.AppLog;
import com.bytedance.applog.ILogger;
import com.bytedance.applog.InitConfig;
import com.bytedance.applog.picker.Picker;
import com.bytedance.applog.util.UriConstants;
import com.bytedance.util.Constant;
import com.bytedance.applog.AppLogConfig;
import com.bytedance.applog.AppLogManager;
import com.bytedance.applog.AppLogABTestManager;

import java.util.Arrays;
import java.util.Map;


/**
 * desc:主页面
 * author: allen
 */
public class MainActivity extends AppCompatActivity {

    private MyApplication application;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        //sdk初始化
        sdkInit();
    }

    /**
     * SDK初始化 - 使用封装后的AppLog
     */
    private void sdkInit() {
        // 方式1：使用自定义配置初始化
        AppLogConfig config = new AppLogConfig.Builder(Constant.appId)
                .channel("test-new")
                .domain("https://gator.uba.ap-southeast-1.volces.com")
                .abDomain("https://tab.ab.ap-southeast-1.volces.com")
                .abEnabled(true)
                .logEnabled(true)
                .pickerEnabled(true)
                .autoTrackEnabled(true)
                .h5BridgeEnabled(true)
                .h5CollectEnabled(true)
                .autoTrackFragmentEnabled(true)
                .encryptEnabled(false)
                .autoStart(true)
                .clipboardEnabled(true)
                .deferredALinkEnabled(true)
                .build();

        AppLogManager.getInstance().initialize(this.getApplication(), config, new AppLogManager.InitializationListener() {
            @Override
            public void onInitSuccess() {
                Log.i("AppLog", "SDK初始化成功");
                // 初始化成功后获取设备信息和AB测试配置
                setupAfterInit();
            }

            @Override
            public void onInitFailure(Exception e) {
                Log.e("AppLog", "SDK初始化失败", e);
            }
        });

        // 设置ALink监听器（广告监测）
        setupALinkListener();
    }

    /**
     * 初始化成功后的设置
     */
    private void setupAfterInit() {
        // 异步获取设备ID
        AppLogManager.getInstance().getDeviceIdAsync(new AppLogManager.DeviceIdCallback() {
            @Override
            public void onDeviceIdReady(String deviceId, String ssid) {
                Log.i("AppLog", "设备ID: " + deviceId);
                Log.i("AppLog", "SSID: " + ssid);
            }

            @Override
            public void onError(Exception e) {
                Log.e("AppLog", "获取设备ID失败", e);
            }
        });

        // 获取AB测试配置
        setupABTest();
    }

    /**
     * 设置AB测试
     */
    private void setupABTest() {
        // 使用封装后的AB测试管理器
        AppLogABTestManager.config("ab1")
                .defaultValue("null")
                .listener(new AppLogABTestManager.ABTestListener() {
                    @Override
                    public void onABTestResult(String experimentKey, String experimentValue) {
                        Log.i("AB测试", experimentKey + " = " + experimentValue);
                        handleABTestResult(experimentKey, experimentValue);
                    }

                    @Override
                    public void onABTestError(String experimentKey, String error) {
                        Log.e("AB测试", "获取失败: " + experimentKey + " - " + error);
                    }
                })
                .get();
    }

    /**
     * 处理AB测试结果
     */
    private void handleABTestResult(String experimentKey, String experimentValue) {
        if ("ab1".equals(experimentKey)) {
            if ("1".equals(experimentValue)) {
                // 实验组1的逻辑
                Log.i("AB测试", "用户在实验组1");
            } else if ("2".equals(experimentValue)) {
                // 实验组2的逻辑
                Log.i("AB测试", "用户在实验组2");
            } else {
                // 对照组的逻辑
                Log.i("AB测试", "用户在对照组");
            }
        }
    }

    /**
     * 设置ALink监听器（广告监测）
     */
    private void setupALinkListener() {
        AppLogManager.getInstance().setALinkListener(new AppLogManager.ALinkListener() {
            @Override
            public void onALinkData(Map<String, String> data, Exception error) {
                if (error == null && data != null) {
                    Log.i("AppLog", "ALink数据: " + data.toString());
                } else {
                    Log.e("AppLog", "ALink数据错误", error);
                }
            }

            @Override
            public void onAttributionData(Map<String, String> data, Exception error) {
                if (error == null && data != null) {
                    Log.i("AppLog", "归因数据: " + data.toString());
                } else {
                    Log.e("AppLog", "归因数据错误", error);
                }
            }
        });
    }


    /**
     * 匿名访问 跳转到子页面
     *
     * @param view
     */
    public void anonymous(View view) {
        Intent intent = new Intent(this, Anonymous.class);
        startActivity(intent);
    }


    /**
     * A用户登录
     *
     * @param view
     */
    public void userLoginA(View view) {
        application = (MyApplication) this.getApplication();
        application.setUserId(Constant.userA);
        System.out.println("----A用户登录访问--->" + application.getUserId());
        Intent intent = new Intent(this, Login.class);
        startActivity(intent);
    }

    /**
     * B用户登录
     *
     * @param view
     */
    public void userLoginB(View view) {
        application = (MyApplication) this.getApplication();
        application.setUserId(Constant.userB);
        System.out.println("----B用户登录访问--->" + application.getUserId());
        Intent intent = new Intent(this, Login.class);
        startActivity(intent);
    }

}