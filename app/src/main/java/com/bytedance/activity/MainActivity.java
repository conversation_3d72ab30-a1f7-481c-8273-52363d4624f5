package com.bytedance.activity;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import com.bytedance.applog.UriConfig;
import com.bytedance.applog.alink.IALinkListener;
import com.bytedance.global.MyApplication;
import com.bytedance.R;
import com.bytedance.applog.AppLog;
import com.bytedance.applog.ILogger;
import com.bytedance.applog.InitConfig;
import com.bytedance.applog.picker.Picker;
import com.bytedance.applog.util.UriConstants;
import com.bytedance.util.Constant;
import com.bytedance.applog.AppLogUtils;

import java.util.Arrays;
import java.util.Map;


/**
 * desc:主页面
 * author: allen
 */
public class MainActivity extends AppCompatActivity {

    private MyApplication application;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        //sdk初始化
        sdkInit();
    }

    /**
     * SDK初始化 - 使用封装后的AppLog
     */
    private void sdkInit() {
        // 使用Kotlin版本的简化初始化
        AppLogUtils.INSTANCE.initDev(this.getApplication(), Constant.appId);
        Log.i("AppLog", "SDK初始化完成");

        // 初始化成功后获取设备信息和AB测试配置
        setupAfterInit();

        // 设置ALink监听器（广告监测）
        setupALinkListener();
    }

    /**
     * 初始化成功后的设置
     */
    private void setupAfterInit() {
        // 使用Kotlin版本获取设备ID
        String deviceId = AppLogUtils.INSTANCE.getDeviceId();
        String ssid = AppLogUtils.INSTANCE.getSSID();
        Log.i("AppLog", "设备ID: " + deviceId);
        Log.i("AppLog", "SSID: " + ssid);

        // 获取AB测试配置
        setupABTest();
    }

    /**
     * 设置AB测试
     */
    private void setupABTest() {
        // 使用Kotlin版本的AB测试
        String ab1Value = AppLogUtils.INSTANCE.getABConfig("ab1", "null");
        Log.i("AB测试", "ab1 = " + ab1Value);
        handleABTestResult("ab1", ab1Value);
    }

    /**
     * 处理AB测试结果
     */
    private void handleABTestResult(String experimentKey, String experimentValue) {
        if ("ab1".equals(experimentKey)) {
            if ("1".equals(experimentValue)) {
                // 实验组1的逻辑
                Log.i("AB测试", "用户在实验组1");
            } else if ("2".equals(experimentValue)) {
                // 实验组2的逻辑
                Log.i("AB测试", "用户在实验组2");
            } else {
                // 对照组的逻辑
                Log.i("AB测试", "用户在对照组");
            }
        }
    }

    /**
     * 设置ALink监听器（广告监测）
     */
    private void setupALinkListener() {
        // 注意：ALink监听器功能需要使用原始SDK的AppLog.setALinkListener
        // 这里暂时注释掉，因为Kotlin版本的AppLogManager需要特殊的接口适配
        Log.i("AppLog", "ALink监听器设置已跳过（需要在Kotlin代码中设置）");
    }


    /**
     * 匿名访问 跳转到子页面
     *
     * @param view
     */
    public void anonymous(View view) {
        Intent intent = new Intent(this, Anonymous.class);
        startActivity(intent);
    }


    /**
     * A用户登录
     *
     * @param view
     */
    public void userLoginA(View view) {
        application = (MyApplication) this.getApplication();
        application.setUserId(Constant.userA);
        System.out.println("----A用户登录访问--->" + application.getUserId());
        Intent intent = new Intent(this, Login.class);
        startActivity(intent);
    }

    /**
     * B用户登录
     *
     * @param view
     */
    public void userLoginB(View view) {
        application = (MyApplication) this.getApplication();
        application.setUserId(Constant.userB);
        System.out.println("----B用户登录访问--->" + application.getUserId());
        Intent intent = new Intent(this, Login.class);
        startActivity(intent);
    }

}