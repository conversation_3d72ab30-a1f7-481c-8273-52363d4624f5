package com.bytedance.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.appcompat.app.AppCompatActivity;

import com.bytedance.event.Event;
import com.bytedance.global.MyApplication;
import com.bytedance.R;
import com.bytedance.util.Constant;


/**
 *  desc:匿名访问功能页
 *  author: allen
 */
public class Anonymous extends AppCompatActivity {

    private MyApplication application ;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.anonymous);
        application = (MyApplication) this.getApplication();
    }

    public void pageView(View view) {
        System.out.println("--pageView--->"+application.getUserId());
        Event event = new Event();
        event.pageView(application.getUserId());
    }

    public void search(View view) {
        System.out.println("--search--->"+application.getUserId());
        Event event = new Event();
        event.search(application.getUserId());
    }

    public void userLoginA(View view) {
        application = (MyApplication) this.getApplication();
        application.setUserId(Constant.userA);
        System.out.println("----登录访问--->"+application.getUserId());
        Intent intent = new Intent(this, Login.class);
        startActivity(intent);
    }

    public void userLoginB(View view) {
        application = (MyApplication) this.getApplication();
        application.setUserId(Constant.userB);
        System.out.println("----登录访问--->"+application.getUserId());
        Intent intent = new Intent(this, Login.class);
        startActivity(intent);
    }

}