package com.bytedance.event;

import com.bytedance.applog.AppLogUtils;

/**
 * 事件上报类 - 使用封装后的AppLog
 * 保持原有接口不变，内部使用新的封装实现
 *
 * <AUTHOR>
 */
public class Event {

    /**
     * 浏览商品事件
     * @param userId 用户ID
     */
    public void pageView(String userId) {
        AppLogUtils.trackPageView(userId, "macBookPro", 20000, "white");
    }

    /**
     * 搜索商品事件
     * @param userId 用户ID
     */
    public void search(String userId) {
        AppLogUtils.trackSearch(userId, "电脑", "/file/computers/details");
    }

    /**
     * 添加购物车
     * @param userId 用户ID
     */
    public void addcart(String userId) {
        AppLogUtils.trackAddCart(userId, "macBookPro", 20000, "white");
    }

    /**
     * 商品付款
     * @param userId 用户ID
     */
    public void pay(String userId) {
        AppLogUtils.trackPay(userId, "macBookPro", 19500, "creditCard");
    }

    /**
     * 用户编辑 - 上传用户属性
     * @param userId 用户ID
     */
    public void userEdit(String userId) {
        AppLogUtils.setUserProfile(userId, "张无忌", "男", 30, "乾坤大挪移");
    }

}
