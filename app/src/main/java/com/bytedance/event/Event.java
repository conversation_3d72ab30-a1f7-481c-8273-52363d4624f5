package com.bytedance.event;
import com.bytedance.applog.AppLog;
import org.json.JSONObject;

public class Event {

    /**
     * 浏览商品事件
     * @param userId
     */
    public void pageView(String userId) {

        JSONObject paramsObj = new JSONObject();
        try {
            if(userId!=null){
                AppLog.setUserUniqueID(userId);
            }
            paramsObj.put("commodity_name","macBookPro"); //事件属性 产品名称
            paramsObj.put("price",20000); //事件属性 价格
            paramsObj.put("color","white"); //事件属性 颜色
            AppLog.onEventV3("pageView", paramsObj);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 搜索商品事件
     * @param userId
     */
    public void search(String userId) {

        JSONObject paramsObj = new JSONObject();
        try {
            if(userId!=null){
                AppLog.setUserUniqueID(userId);
            }
            paramsObj.put("search_keywords","电脑"); //事件属性 搜索关键词
            paramsObj.put("result_details","/file/computers/details"); //事件属性 搜索详情页

            //绑定业务对象实例(如有业务对象场景需要,前提在数据管理业务对象下先创建该业务对象)
//          paramsObj.put("__items","[{\"surface\":[{\"id\":\"100001\"}]}]");

            AppLog.onEventV3("search", paramsObj);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 添加购物车
     * @param userId
     */
    public void addcart(String userId) {

        JSONObject paramsObj = new JSONObject();
        try {
            if(userId!=null){
                AppLog.setUserUniqueID(userId);
            }
            paramsObj.put("commodity_name","macBookPro"); //事件属性 产品名称
            paramsObj.put("price",20000); //事件属性 价格
            paramsObj.put("color","white"); //事件属性 颜色
            AppLog.onEventV3("addcart", paramsObj);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 商品付款
     * @param userId
     */
    public void pay(String userId) {
        if(userId==null){
            return ;
        }
        JSONObject paramsObj = new JSONObject();
        try {
            AppLog.setUserUniqueID(userId);
            paramsObj.put("commodity_name","macBookPro"); //事件属性 产品名称
            paramsObj.put("price",19500); //事件属性 付款价格
            paramsObj.put("pay_method","creditCard"); // 付款方式
            AppLog.onEventV3("pay", paramsObj);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 用户编辑 - 上传用户属性
     * @param userId
     */
    public void userEdit(String userId) {
        if(userId==null){
            return ;
        }
        JSONObject paramsObj = new JSONObject();
        try {
            AppLog.setUserUniqueID(userId);
            paramsObj.put("user_name1","张无忌");
            paramsObj.put("user_gender1","男");
            paramsObj.put("user_age1",30);
            paramsObj.put("user_skill","乾坤大挪移");
            AppLog.profileSet(paramsObj);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
