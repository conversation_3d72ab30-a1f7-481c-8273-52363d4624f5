<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.Login">
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/userId"
        android:text=""
        android:textSize="34sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.042" />

    <Button
        android:id="@+id/pageView"
        android:layout_width="300dp"
        android:layout_height="50dp"
        android:layout_marginTop="100dp"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:onClick="pageView"
        android:text="商品浏览"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.544"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <Button
        android:id="@+id/search"
        android:layout_width="300dp"
        android:layout_height="50dp"
        android:layout_marginTop="180dp"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:onClick="search"
        android:text="商品搜索"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.544"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <Button
        android:id="@+id/addcart"
        android:layout_width="300dp"
        android:layout_height="50dp"
        android:layout_marginTop="260dp"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:onClick="addcart"
        android:text="添加购物车"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.544"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <Button
        android:id="@+id/pay"
        android:layout_width="300dp"
        android:layout_height="50dp"
        android:layout_marginTop="340dp"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:onClick="pay"
        android:text="订单支付"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.544"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/userEdit"
        android:layout_width="300dp"
        android:layout_height="50dp"
        android:layout_marginTop="420dp"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:onClick="userEdit"
        android:text="用户信息修改"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.544"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <Button
        android:id="@+id/logout"
        android:layout_width="300dp"
        android:layout_height="50dp"
        android:layout_marginTop="500dp"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:onClick="logout"
        android:text="退出登录"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.544"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />









</androidx.constraintlayout.widget.ConstraintLayout>