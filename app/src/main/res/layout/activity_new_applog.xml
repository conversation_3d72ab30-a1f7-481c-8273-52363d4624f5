<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="AppLog 封装库使用示例"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="事件上报示例"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <Button
            android:id="@+id/btn_track_page_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="浏览商品事件"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_track_search"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="搜索事件"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_track_add_cart"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="添加购物车事件"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_track_pay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="支付事件"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_custom_event"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="自定义事件"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="用户属性示例"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <Button
            android:id="@+id/btn_set_user_profile"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="设置用户属性"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="设备信息和AB测试"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <Button
            android:id="@+id/btn_get_device_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="获取设备信息"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_test_ab"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="测试AB配置"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="使用说明"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="1. 点击按钮测试各种事件上报功能\n2. 查看Logcat输出查看详细日志\n3. 支付和用户属性功能需要先登录\n4. AB测试配置会在后台获取并应用"
            android:textSize="14sp"
            android:lineSpacingExtra="4dp"
            android:layout_marginBottom="16dp" />

    </LinearLayout>

</ScrollView>
