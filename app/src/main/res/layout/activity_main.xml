<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.MainActivity">


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="匿名用户"
        android:textSize="34sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.042" />


    <Button
        android:id="@+id/anonymous"
        android:layout_width="300dp"
        android:layout_height="50dp"
        android:layout_marginTop="150dp"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:onClick="anonymous"
        android:text="匿名访问"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.544"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <Button
        android:id="@+id/userLoginA"
        android:layout_width="300dp"
        android:layout_height="50dp"
        android:layout_marginTop="300dp"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:onClick="userLoginA"
        android:text="用户登录A"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.544"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <Button
        android:id="@+id/userLoginB"
        android:layout_width="300dp"
        android:layout_height="50dp"
        android:layout_marginTop="450dp"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:onClick="userLoginB"
        android:text="用户登录B"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.544"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>