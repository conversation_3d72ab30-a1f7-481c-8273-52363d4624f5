<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="AppLog Kotlin 封装库使用示例"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center"
            android:textColor="@android:color/holo_blue_dark"
            android:layout_marginBottom="24dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🚀 Kotlin 特性展示"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <Button
            android:id="@+id/btn_test_extensions_kt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="测试 Kotlin 扩展函数"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_test_dsl_kt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="测试 DSL 风格 API"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="📊 事件上报示例"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <Button
            android:id="@+id/btn_track_page_view_kt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="浏览商品事件 (多种 Kotlin 用法)"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_track_search_kt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="搜索事件 (扩展函数)"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_track_add_cart_kt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="添加购物车事件"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_track_pay_kt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="支付事件 (空值安全)"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_custom_event_kt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="自定义事件 (DSL + 条件追踪)"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="👤 用户属性示例"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <Button
            android:id="@+id/btn_set_user_profile_kt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="设置用户属性 (DSL 风格)"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🧪 设备信息和AB测试"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <Button
            android:id="@+id/btn_get_device_info_kt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="获取设备信息 (协程版本)"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_test_ab_kt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="测试AB配置 (多种 Kotlin 用法)"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="📖 Kotlin 特性说明"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="✨ 本示例展示的 Kotlin 特性：\n\n• DSL 风格 API - 更直观的配置和调用\n• 扩展函数 - 为现有类型添加新功能\n• 协程支持 - 异步操作更简洁\n• 空值安全 - 避免 NullPointerException\n• 高阶函数 - 函数式编程支持\n• 数据类 - 简化配置管理\n• 对象声明 - 单例模式更简洁\n• 内联函数 - 性能优化\n• 智能类型转换 - 减少显式转换\n• 解构声明 - 简化数据提取"
            android:textSize="14sp"
            android:lineSpacingExtra="4dp"
            android:background="@android:color/background_light"
            android:padding="12dp"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🔧 使用说明"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="1. 点击按钮测试各种 Kotlin 特性\n2. 查看 Logcat 输出查看详细日志\n3. 支付和用户属性功能需要先登录\n4. AB测试配置会在后台获取并应用\n5. 协程操作在后台线程执行\n6. 扩展函数提供更简洁的API\n7. DSL 风格让配置更直观"
            android:textSize="14sp"
            android:lineSpacingExtra="4dp"
            android:layout_marginBottom="16dp" />

    </LinearLayout>

</ScrollView>
