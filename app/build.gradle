plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

//全埋点引用plugin，不需要请注释掉
apply plugin: 'com.bytedance.std.tracker'

android {
    compileSdk 32

    defaultConfig {
        applicationId "com.bible.debug"
        minSdk 21
        targetSdk 32
        versionCode 1
        versionName "1.0"


        //埋点验证或圈选功能需要配置URL_SCHEME,请修改
        manifestPlaceholders= ["APPLOG_SCHEME": "rangersapplog.xxxxxxxxxxxx"]
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'com.google.android.material:material:1.5.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.3'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    //sdk引入6.10.0,如使用最新版本，请参考官网文档最新版本号
    implementation 'com.bytedance.applog:RangersAppLog-All-cn:6.10.0'
    //scheme模块,用于埋点验证/圈选功能
    implementation 'com.bytedance.applog:RangersAppLog-All-scheme:6.10.0'

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.10"
    implementation "androidx.core:core-ktx:1.9.0"




}

