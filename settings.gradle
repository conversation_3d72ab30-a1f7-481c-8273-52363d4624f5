pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
        jcenter()

        //配置火山maven仓库
        maven {
            url 'https://artifact.bytedance.com/repository/Volcengine/'
        }
    }

}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        jcenter()

        //配置火山maven仓库
        maven {
            url 'https://artifact.bytedance.com/repository/Volcengine/'
        }
    }

}
rootProject.name = "andriodDemo"
include ':app'

